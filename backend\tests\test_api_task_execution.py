"""
Tests for TaskExecutionAgent REST API endpoints.

Tests the HTTP API integration with TaskExecutionAgent including:
- Task plan generation and execution
- Module integration wrappers
- Backward compatibility
- Error handling
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, AsyncMock

from backend.src.main import app
from backend.src.state.project_state import ProjectState, ProjectStatus


class TestTaskExecutionAPI:
    """Test TaskExecutionAgent REST API endpoints"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)
    
    @pytest.fixture
    def mock_session_manager(self):
        """Mock session manager with test project"""
        project_state = ProjectState(
            session_id="test_session",
            project_brief="Test project for API testing",
            status=ProjectStatus.QUILTING,
            target_patterns=["rest_api", "database"]
        )
        
        with patch('backend.src.api.task_execution.session_manager') as mock_manager:
            mock_manager.get_project.return_value = project_state
            yield mock_manager, project_state
    
    def test_generate_task_plan_success(self, client, mock_session_manager):
        """Test successful task plan generation"""
        mock_manager, project_state = mock_session_manager
        
        # Mock TaskExecutionAgent
        with patch('backend.src.api.task_execution.TaskExecutionAgent') as mock_agent_class:
            mock_agent = mock_agent_class.return_value

            # Create proper mock tasks
            mock_task1 = type('Task', (), {
                'to_dict': lambda self: {
                    'id': 'task1',
                    'description': 'Test task 1',
                    'status': 'pending'
                }
            })()
            mock_task2 = type('Task', (), {
                'to_dict': lambda self: {
                    'id': 'task2',
                    'description': 'Test task 2',
                    'status': 'pending'
                }
            })()

            mock_agent.plan = AsyncMock(return_value=(
                [mock_task1, mock_task2],
                "Test rationale for the plan"
            ))
            
            response = client.post(
                "/api/projects/test_session/tasks/plan",
                json={
                    "goal": "add authentication to project",
                    "context": {"framework": "express"}
                }
            )
            
            if response.status_code != 200:
                print(f"Error response: {response.json()}")
            assert response.status_code == 200
            data = response.json()
            
            # Verify response structure
            assert data["goal"] == "add authentication to project"
            assert data["rationale"] == "Test rationale for the plan"  # Gemini Principle 1
            assert data["task_count"] == 2
            assert len(data["tasks"]) == 2
            assert data["tasks"][0]["description"] == "Test task 1"
            assert data["tasks"][1]["description"] == "Test task 2"
            
            # Verify agent was called correctly
            mock_agent.plan.assert_called_once_with(
                "add authentication to project",
                {"framework": "express"}
            )
    
    def test_generate_task_plan_project_not_found(self, client):
        """Test task plan generation with non-existent project"""
        with patch('backend.src.api.task_execution.session_manager') as mock_manager:
            mock_manager.get_project.return_value = None
            
            response = client.post(
                "/api/projects/nonexistent/tasks/plan",
                json={"goal": "test goal"}
            )
            
            assert response.status_code == 404
            assert "Project not found" in response.json()["detail"]
    
    def test_execute_task_plan_success(self, client, mock_session_manager):
        """Test successful task plan execution"""
        mock_manager, project_state = mock_session_manager
        
        # Mock TaskExecutionAgent
        with patch('backend.src.api.task_execution.TaskExecutionAgent') as mock_agent_class:
            mock_agent = mock_agent_class.return_value
            
            # Mock execution result
            mock_completed_task = type('Task', (), {
                'to_dict': lambda self: {'id': 'task1', 'status': 'completed'}
            })()

            mock_result = type('ExecutionResult', (), {
                'success': True,
                'summary_message': 'All tasks completed successfully',
                'completed_tasks': [mock_completed_task],
                'failed_task': None,
                'total_duration_seconds': 30.5,
                'to_dict': lambda self: {
                    'suggested_actions': [
                        {'action_type': 'download_project', 'title': 'Download Project'}
                    ]
                }
            })()
            
            mock_agent.execute = AsyncMock(return_value=mock_result)
            
            response = client.post(
                "/api/projects/test_session/tasks/execute",
                json={
                    "tasks": [
                        {"id": "task1", "description": "Test task", "metadata": {}}
                    ],
                    "approved": True
                }
            )
            
            if response.status_code != 200:
                print(f"Execute error response: {response.json()}")
            assert response.status_code == 200
            data = response.json()
            
            # Verify response structure
            assert data["success"] is True
            assert data["summary_message"] == "All tasks completed successfully"
            assert len(data["completed_tasks"]) == 1
            assert data["failed_task"] is None
            assert data["total_duration_seconds"] == 30.5
            assert len(data["suggested_actions"]) == 1  # Gemini Principle 3
            assert data["suggested_actions"][0]["action_type"] == "download_project"
    
    def test_execute_task_plan_not_approved(self, client, mock_session_manager):
        """Test task execution with unapproved plan"""
        mock_manager, project_state = mock_session_manager
        
        response = client.post(
            "/api/projects/test_session/tasks/execute",
            json={
                "tasks": [{"id": "task1", "description": "Test task"}],
                "approved": False
            }
        )
        
        if response.status_code != 400:
            print(f"Not approved error response: {response.json()}")
        assert response.status_code == 400
        assert "Task plan not approved" in response.json()["detail"]
    
    def test_component_search_task_success(self, client, mock_session_manager):
        """Test component search task execution"""
        mock_manager, project_state = mock_session_manager
        
        with patch('backend.src.api.task_execution.TaskExecutionAgent') as mock_agent_class:
            mock_agent = mock_agent_class.return_value
            
            # Mock plan generation
            mock_search_task = type('Task', (), {
                'to_dict': lambda self: {'id': 'search_task'}
            })()
            mock_agent.plan = AsyncMock(return_value=(
                [mock_search_task],
                "Component search rationale"
            ))
            
            # Mock execution
            mock_completed_search_task = type('Task', (), {
                'to_dict': lambda self: {'id': 'search_task'}
            })()
            mock_result = type('ExecutionResult', (), {
                'success': True,
                'summary_message': 'Component search completed',
                'completed_tasks': [mock_completed_search_task],
                'failed_task': None,
                'total_duration_seconds': 15.0,
                'to_dict': lambda self: {'suggested_actions': []}
            })()
            mock_agent.execute = AsyncMock(return_value=mock_result)
            
            # Mock the component search integration
            with patch('backend.src.api.task_execution._perform_actual_component_search') as mock_search:
                mock_search.return_value = None
                
                response = client.post("/api/projects/test_session/tasks/component-search")
                
                assert response.status_code == 200
                data = response.json()
                
                assert data["success"] is True
                assert data["summary_message"] == "Component search completed"
                
                # Verify agent was called with correct context
                mock_agent.plan.assert_called_once()
                call_args = mock_agent.plan.call_args
                assert call_args[0][0] == "search for components matching project requirements"
                assert "patterns" in call_args[0][1]
                assert "project_brief" in call_args[0][1]
    
    def test_code_generation_task_success(self, client, mock_session_manager):
        """Test code generation task execution"""
        mock_manager, project_state = mock_session_manager
        
        # Add selected components to project state
        project_state.selected_components = {
            "rest_api": {"name": "express", "url": "https://github.com/expressjs/express"},
            "database": {"name": "mongoose", "url": "https://github.com/Automattic/mongoose"}
        }
        
        with patch('backend.src.api.task_execution.TaskExecutionAgent') as mock_agent_class:
            mock_agent = mock_agent_class.return_value
            
            # Mock plan generation
            mock_codegen_task = type('Task', (), {
                'to_dict': lambda self: {'id': 'codegen_task'}
            })()
            mock_agent.plan = AsyncMock(return_value=(
                [mock_codegen_task],
                "Code generation rationale"
            ))
            
            # Mock execution
            mock_completed_codegen_task = type('Task', (), {
                'to_dict': lambda self: {'id': 'codegen_task'}
            })()
            mock_result = type('ExecutionResult', (), {
                'success': True,
                'summary_message': 'Code generation completed',
                'completed_tasks': [mock_completed_codegen_task],
                'failed_task': None,
                'total_duration_seconds': 45.0,
                'to_dict': lambda self: {'suggested_actions': []}
            })()
            mock_agent.execute = AsyncMock(return_value=mock_result)
            
            response = client.post("/api/projects/test_session/tasks/code-generation")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["success"] is True
            assert data["summary_message"] == "Code generation completed"
            
            # Verify agent was called with correct context
            mock_agent.plan.assert_called_once()
            call_args = mock_agent.plan.call_args
            assert call_args[0][0] == "generate code for selected components"
            assert "selected_components" in call_args[0][1]
            assert "target_patterns" in call_args[0][1]
    
    def test_code_generation_no_components(self, client, mock_session_manager):
        """Test code generation without selected components"""
        mock_manager, project_state = mock_session_manager
        
        # Ensure no selected components
        project_state.selected_components = {}
        
        response = client.post("/api/projects/test_session/tasks/code-generation")
        
        assert response.status_code == 400
        assert "No components selected" in response.json()["detail"]
    
    def test_get_task_execution_status(self, client, mock_session_manager):
        """Test task execution status endpoint"""
        mock_manager, project_state = mock_session_manager
        
        # Set up active tasks
        project_state.active_task_list = [
            {"id": "task1", "description": "Active task 1"},
            {"id": "task2", "description": "Active task 2"}
        ]
        project_state.task_execution_context = {"execution_start": "2025-01-20T10:00:00"}
        
        response = client.get("/api/projects/test_session/tasks/status")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["session_id"] == "test_session"
        assert data["has_active_tasks"] is True
        assert data["active_task_count"] == 2
        assert data["task_execution_context"]["execution_start"] == "2025-01-20T10:00:00"
        assert data["project_status"] == "quilting"
        assert "last_modified" in data
    
    def test_get_task_execution_status_no_active_tasks(self, client, mock_session_manager):
        """Test task execution status with no active tasks"""
        mock_manager, project_state = mock_session_manager
        
        # Ensure no active tasks
        project_state.active_task_list = None
        project_state.task_execution_context = None
        
        response = client.get("/api/projects/test_session/tasks/status")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["has_active_tasks"] is False
        assert data["active_task_count"] == 0
        assert data["task_execution_context"] is None
    
    def test_error_handling(self, client, mock_session_manager):
        """Test error handling in task execution endpoints"""
        mock_manager, project_state = mock_session_manager
        
        # Mock TaskExecutionAgent to raise exception
        with patch('backend.src.api.task_execution.TaskExecutionAgent') as mock_agent_class:
            mock_agent = mock_agent_class.return_value
            mock_agent.plan = AsyncMock(side_effect=Exception("Test error"))
            
            response = client.post(
                "/api/projects/test_session/tasks/plan",
                json={"goal": "test goal"}
            )
            
            assert response.status_code == 500
            assert "Plan generation failed" in response.json()["detail"]
            assert "Test error" in response.json()["detail"]
