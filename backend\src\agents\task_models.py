"""
Core data models for TaskExecutionAgent.

Implements Gemini's three principles:
1. Execution Rationale - Tasks include reasoning for why they're needed
2. Agent Activity Instrumentation - current_activity field for micro-progress
3. Post-Execution User Actions - Suggested next actions after completion
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
import uuid


class TaskStatus(Enum):
    """Status of a task in the execution pipeline"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


class SuggestedActionType(Enum):
    """Types of suggested actions for post-execution user guidance"""
    DOWNLOAD_PROJECT = "download_project"
    START_NEW_GOAL = "start_new_goal"
    VIEW_DOCUMENTATION = "view_documentation"
    RETRY_PLAN = "retry_plan"
    EDIT_GOAL_REPLAN = "edit_goal_replan"
    REPORT_ISSUE = "report_issue"


@dataclass
class SuggestedAction:
    """
    Suggested action for user after task execution completes.
    
    Implements Gemini Principle 3: Post-Execution User Actions
    """
    action_type: SuggestedActionType
    title: str  # Display title for the action
    description: str  # Explanation of what this action does
    enabled: bool = True  # Whether this action is currently available
    metadata: Optional[Dict[str, Any]] = None  # Additional action-specific data


@dataclass
class Task:
    """
    Generic task object for TaskExecutionAgent execution.
    
    Implements Gemini Principle 2: Agent Activity Instrumentation
    with current_activity field for micro-progress updates.
    """
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    description: str = ""  # Human-readable action description
    status: TaskStatus = TaskStatus.PENDING
    
    # Gemini Principle 2: Agent Activity Instrumentation
    current_activity: Optional[str] = None  # Real-time micro-progress updates
    
    # Lifecycle tracking
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None
    
    # Flexible extension point for task-specific data
    metadata: Optional[Dict[str, Any]] = field(default_factory=dict)
    
    def start(self, activity: str = None) -> None:
        """Mark task as started with optional initial activity"""
        self.status = TaskStatus.IN_PROGRESS
        self.start_time = datetime.now()
        if activity:
            self.current_activity = activity
    
    def update_activity(self, activity: str) -> None:
        """Update current activity for real-time progress feedback"""
        self.current_activity = activity
    
    def complete(self, final_activity: str = None) -> None:
        """Mark task as completed"""
        self.status = TaskStatus.COMPLETED
        self.end_time = datetime.now()
        if final_activity:
            self.current_activity = final_activity
        else:
            self.current_activity = "Completed"
    
    def fail(self, error_message: str, activity: str = None) -> None:
        """Mark task as failed with error details"""
        self.status = TaskStatus.FAILED
        self.end_time = datetime.now()
        self.error_message = error_message
        if activity:
            self.current_activity = activity
        else:
            self.current_activity = f"Failed: {error_message}"
    
    def get_duration_seconds(self) -> Optional[float]:
        """Get task duration in seconds if completed"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "id": self.id,
            "description": self.description,
            "status": self.status.value,
            "current_activity": self.current_activity,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "error_message": self.error_message,
            "duration_seconds": self.get_duration_seconds(),
            "metadata": self.metadata
        }


@dataclass
class ExecutionResult:
    """
    Result of task execution with suggested next actions.
    
    Implements Gemini Principle 3: Post-Execution User Actions
    """
    success: bool
    completed_tasks: List[Task]
    failed_task: Optional[Task] = None
    total_duration_seconds: float = 0.0
    
    # Gemini Principle 3: Suggested next actions based on execution outcome
    suggested_actions: List[SuggestedAction] = field(default_factory=list)
    
    # Summary information
    summary_message: str = ""
    details: Optional[Dict[str, Any]] = field(default_factory=dict)
    
    def add_suggested_action(self, action: SuggestedAction) -> None:
        """Add a suggested action for the user"""
        self.suggested_actions.append(action)
    
    def get_success_actions(self) -> List[SuggestedAction]:
        """Get suggested actions for successful completion"""
        return [
            SuggestedAction(
                action_type=SuggestedActionType.DOWNLOAD_PROJECT,
                title="Download Project",
                description="Download the generated codebase as a ZIP file"
            ),
            SuggestedAction(
                action_type=SuggestedActionType.START_NEW_GOAL,
                title="Start New Goal",
                description="Give me a new high-level goal for this project"
            ),
            SuggestedAction(
                action_type=SuggestedActionType.VIEW_DOCUMENTATION,
                title="View Documentation",
                description="Open the generated README file"
            )
        ]
    
    def get_failure_actions(self, allow_retry: bool = True) -> List[SuggestedAction]:
        """Get suggested actions for failed execution"""
        actions = []
        
        if allow_retry:
            actions.append(SuggestedAction(
                action_type=SuggestedActionType.RETRY_PLAN,
                title="Retry Plan",
                description="Attempt to run the entire plan again"
            ))
        
        actions.extend([
            SuggestedAction(
                action_type=SuggestedActionType.EDIT_GOAL_REPLAN,
                title="Edit Goal & Re-plan",
                description="Modify your original request and generate a new plan"
            ),
            SuggestedAction(
                action_type=SuggestedActionType.REPORT_ISSUE,
                title="Report Issue",
                description="Help us improve by reporting this failure"
            )
        ])
        
        return actions
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "success": self.success,
            "completed_tasks": [task.to_dict() for task in self.completed_tasks],
            "failed_task": self.failed_task.to_dict() if self.failed_task else None,
            "total_duration_seconds": self.total_duration_seconds,
            "suggested_actions": [
                {
                    "action_type": action.action_type.value,
                    "title": action.title,
                    "description": action.description,
                    "enabled": action.enabled,
                    "metadata": action.metadata
                }
                for action in self.suggested_actions
            ],
            "summary_message": self.summary_message,
            "details": self.details
        }
