"""
Tests for AdapterGenerator - Core adapter generation engine for component integration.

Tests the adapter generation functionality, pattern detection, and quality metrics.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from backend.src.modules.code_generation.adapter_generator import (
    AdapterGenerator, AdapterPattern
)
from backend.src.modules.code_generation.context_weaver import ContextWeaver
from backend.src.modules.code_generation.specialized_llm_client import SpecializedLLMClient
from backend.src.state.components import (
    ComponentAPI, AdapterCode, GenerationContext, CodeGenerationType,
    IntegrationComplexity
)
from backend.src.state.project_state import ProjectState, ProjectStatus


class TestAdapterGenerator:
    """Test AdapterGenerator functionality"""
    
    @pytest.fixture
    def mock_context_weaver(self):
        """Create mock ContextWeaver"""
        mock = Mock(spec=ContextWeaver)
        mock.build_generation_context.return_value = GenerationContext(
            goal="Test adapter",
            generation_type=CodeGenerationType.ADAPTER,
            project_framework="fastapi",
            project_language="python"
        )
        mock.build_llm_prompt.return_value = "Test prompt for adapter generation"
        return mock
    
    @pytest.fixture
    def mock_llm_client(self):
        """Create mock SpecializedLLMClient"""
        mock = Mock(spec=SpecializedLLMClient)
        mock.generate_code = AsyncMock(return_value={
            "success": True,
            "generated_code": "# Generated adapter code\nclass TestAdapter:\n    pass",
            "model_used": "claude-3-sonnet",
            "token_usage": 150,
            "cost_estimate": 0.15
        })
        return mock
    
    @pytest.fixture
    def adapter_generator(self, mock_context_weaver, mock_llm_client):
        """Create AdapterGenerator instance for testing"""
        return AdapterGenerator(
            context_weaver=mock_context_weaver,
            llm_client=mock_llm_client
        )
    
    @pytest.fixture
    def sample_project_state(self):
        """Create sample project state with component data"""
        project_state = ProjectState(
            session_id="test-session",
            project_name="Test Integration Project",
            project_brief="Integration between auth service and user service",
            target_patterns=["rest_api"],
            status=ProjectStatus.QUILTING
        )
        
        # Add component API data
        project_state.component_apis["auth-service"] = {
            "component_name": "auth-service",
            "language": "python",
            "exported_classes": [{"name": "AuthManager"}],
            "public_methods": [{"name": "authenticate"}],
            "dependencies": ["fastapi", "pyjwt"]
        }
        
        project_state.component_apis["user-service"] = {
            "component_name": "user-service",
            "language": "python",
            "exported_classes": [{"name": "UserService"}],
            "public_methods": [{"name": "get_user"}],
            "dependencies": ["sqlalchemy", "pydantic"]
        }
        
        return project_state
    
    def test_adapter_patterns_initialization(self, adapter_generator):
        """Test adapter pattern configurations"""
        patterns = adapter_generator.adapter_patterns
        
        # Verify all expected patterns are present
        expected_patterns = [
            "rest_api", "message_queue", "database", "authentication",
            "configuration", "event_driven", "generic"
        ]
        
        for pattern in expected_patterns:
            assert pattern in patterns
            
            # Verify pattern structure
            pattern_config = patterns[pattern]
            assert "description" in pattern_config
            assert "complexity" in pattern_config
            assert "required_capabilities" in pattern_config
            assert "common_patterns" in pattern_config
            assert "template_hints" in pattern_config
    
    def test_determine_adapter_pattern_rest_api(self, adapter_generator, sample_project_state):
        """Test adapter pattern determination for REST API"""
        goals = [
            "Create API adapter for authentication",
            "Connect REST endpoint to user service",
            "HTTP integration between components"
        ]
        
        for goal in goals:
            pattern = adapter_generator._determine_adapter_pattern(goal, sample_project_state)
            assert pattern == AdapterPattern.REST_API
    
    def test_determine_adapter_pattern_message_queue(self, adapter_generator, sample_project_state):
        """Test adapter pattern determination for message queue"""
        goals = [
            "Create message queue adapter",
            "Publish events to queue system",
            "Subscribe to message notifications"
        ]
        
        for goal in goals:
            pattern = adapter_generator._determine_adapter_pattern(goal, sample_project_state)
            assert pattern == AdapterPattern.MESSAGE_QUEUE
    
    def test_determine_adapter_pattern_database(self, adapter_generator, sample_project_state):
        """Test adapter pattern determination for database"""
        goals = [
            "Connect to database for user storage",
            "SQL query adapter for data access",
            "Database integration layer"
        ]
        
        for goal in goals:
            pattern = adapter_generator._determine_adapter_pattern(goal, sample_project_state)
            assert pattern == AdapterPattern.DATABASE
    
    def test_determine_adapter_pattern_authentication(self, adapter_generator, sample_project_state):
        """Test adapter pattern determination for authentication"""
        goals = [
            "JWT authentication adapter",
            "OAuth login integration",
            "Token-based auth system"
        ]
        
        for goal in goals:
            pattern = adapter_generator._determine_adapter_pattern(goal, sample_project_state)
            assert pattern == AdapterPattern.AUTHENTICATION
    
    def test_determine_adapter_pattern_fallback(self, adapter_generator, sample_project_state):
        """Test adapter pattern fallback to project patterns"""
        goal = "Generic integration between components"
        pattern = adapter_generator._determine_adapter_pattern(goal, sample_project_state)
        
        # Should fall back to REST API based on project patterns
        assert pattern == AdapterPattern.REST_API
    
    @pytest.mark.asyncio
    async def test_generate_adapter_success(self, adapter_generator, sample_project_state):
        """Test successful adapter generation"""
        result = await adapter_generator.generate_adapter(
            goal="Create authentication adapter",
            source_component="auth-service",
            target_component="user-service",
            project_state=sample_project_state,
            quality_level="production"
        )
        
        # Verify result structure
        assert isinstance(result, AdapterCode)
        assert result.source_component == "auth-service"
        assert result.target_component == "user-service"
        assert result.pattern_type == "authentication"
        assert result.language == "python"
        assert len(result.adapter_code) > 0
        assert result.complexity_score > 0
        assert result.file_path.endswith("_adapter.py")
    
    @pytest.mark.asyncio
    async def test_generate_adapter_with_specific_pattern(self, adapter_generator, sample_project_state):
        """Test adapter generation with specific pattern"""
        result = await adapter_generator.generate_adapter(
            goal="Connect components",
            source_component="auth-service",
            target_component="user-service",
            project_state=sample_project_state,
            adapter_pattern=AdapterPattern.REST_API,
            quality_level="production"
        )
        
        assert result.pattern_type == "rest_api"
        assert "api_adapter.py" in result.file_path
    
    @pytest.mark.asyncio
    async def test_generate_multiple_adapters(self, adapter_generator, sample_project_state):
        """Test batch adapter generation"""
        adapter_requests = [
            {
                "goal": "Auth to user adapter",
                "source_component": "auth-service",
                "target_component": "user-service"
            },
            {
                "goal": "User to database adapter",
                "source_component": "user-service",
                "target_component": "database",
                "adapter_pattern": AdapterPattern.DATABASE
            }
        ]
        
        results = await adapter_generator.generate_multiple_adapters(
            adapter_requests, sample_project_state, "production"
        )
        
        assert len(results) == 2
        assert all(isinstance(result, AdapterCode) for result in results)
        assert results[0].source_component == "auth-service"
        assert results[1].pattern_type == "database"
    
    def test_generate_adapter_filename(self, adapter_generator):
        """Test adapter filename generation"""
        test_cases = [
            ("auth-service", "user-service", AdapterPattern.REST_API, "auth_service_to_user_service_api_adapter.py"),
            ("message-queue", "processor", AdapterPattern.MESSAGE_QUEUE, "message_queue_to_processor_queue_adapter.py"),
            ("app", "database", AdapterPattern.DATABASE, "app_to_database_db_adapter.py"),
            ("client", "auth", AdapterPattern.AUTHENTICATION, "client_to_auth_auth_adapter.py"),
            ("service", "config", AdapterPattern.CONFIGURATION, "service_to_config_config_adapter.py"),
            ("producer", "consumer", AdapterPattern.EVENT_DRIVEN, "producer_to_consumer_event_adapter.py"),
            ("source", "target", AdapterPattern.GENERIC, "source_to_target_adapter.py")
        ]
        
        for source, target, pattern, expected in test_cases:
            result = adapter_generator._generate_adapter_filename(source, target, pattern)
            assert result == expected
    
    def test_calculate_quality_score(self, adapter_generator):
        """Test quality score calculation"""
        test_cases = [
            ({"model_used": "claude-3-sonnet", "generated_code": "x" * 1500}, 0.9),
            ({"model_used": "deepseek-coder", "generated_code": "x" * 800}, 0.8),
            ({"model_used": "unknown", "generated_code": "x" * 100}, 0.6),
            ({"model_used": "gpt-4", "generated_code": "x" * 2000}, 1.0)
        ]
        
        for generation_result, expected_min in test_cases:
            score = adapter_generator._calculate_quality_score(generation_result)
            assert score >= expected_min - 0.1  # Allow small variance
            assert 0.0 <= score <= 1.0
    
    def test_extract_dependencies_python(self, adapter_generator):
        """Test dependency extraction from Python code"""
        context = GenerationContext(
            goal="test", generation_type=CodeGenerationType.ADAPTER,
            project_language="python"
        )
        
        code = """
import fastapi
from sqlalchemy import create_engine
import os
from typing import Dict
import requests
"""
        
        deps = adapter_generator._extract_dependencies(code, context)
        
        # Should extract third-party dependencies, skip stdlib
        assert "fastapi" in deps
        assert "sqlalchemy" in deps
        assert "requests" in deps
        assert "os" not in deps  # stdlib
        assert "typing" not in deps  # stdlib
    
    def test_extract_dependencies_javascript(self, adapter_generator):
        """Test dependency extraction from JavaScript code"""
        context = GenerationContext(
            goal="test", generation_type=CodeGenerationType.ADAPTER,
            project_language="javascript"
        )
        
        code = """
const express = require('express');
const axios = require('axios');
import React from 'react';
const fs = require('fs');
"""
        
        deps = adapter_generator._extract_dependencies(code, context)
        
        # Should extract third-party dependencies
        assert "express" in deps
        assert "axios" in deps
        assert "react" in deps
    
    def test_create_fallback_adapter(self, adapter_generator):
        """Test fallback adapter creation"""
        result = adapter_generator._create_fallback_adapter(
            "source-service", "target-service", "Test error message"
        )
        
        assert isinstance(result, AdapterCode)
        assert result.source_component == "source-service"
        assert result.target_component == "target-service"
        assert result.pattern_type == "fallback"
        assert result.complexity_score == 0.2  # Low complexity for fallback
        assert "Test error message" in result.documentation
    
    def test_enhance_context_for_adapter(self, adapter_generator):
        """Test context enhancement for adapter generation"""
        context = GenerationContext(
            goal="test", generation_type=CodeGenerationType.ADAPTER
        )
        
        adapter_generator._enhance_context_for_adapter(context, AdapterPattern.REST_API)
        
        assert context.adapter_pattern == "rest_api"
        assert context.adapter_complexity == IntegrationComplexity.MEDIUM
        assert "http_client" in context.required_capabilities
        assert len(context.template_hints) > 0
    
    def test_enhance_context_high_complexity(self, adapter_generator):
        """Test context enhancement for high complexity adapters"""
        context = GenerationContext(
            goal="test", generation_type=CodeGenerationType.ADAPTER
        )
        
        adapter_generator._enhance_context_for_adapter(context, AdapterPattern.MESSAGE_QUEUE)
        
        assert context.adapter_complexity == IntegrationComplexity.HIGH
        assert context.quality_level == "enterprise"
        assert context.include_tests is True
        assert context.include_documentation is True
    
    def test_track_generation_stats(self, adapter_generator):
        """Test generation statistics tracking"""
        adapter_code = AdapterCode(
            source_component="source",
            target_component="target",
            pattern_type="rest_api",
            adapter_code="# This is a test adapter with more than 100 characters\n" * 5,  # Make it long enough
            file_path="test_adapter.py",
            language="python",
            complexity_score=0.4  # Medium complexity
        )
        
        start_time = datetime.now()
        # Simulate some time passing
        import time
        time.sleep(0.01)  # 10ms
        adapter_generator._track_generation_stats(
            AdapterPattern.REST_API, adapter_code, start_time
        )
        
        stats = adapter_generator.generation_stats
        assert stats["total_adapters_generated"] == 1
        assert stats["successful_generations"] == 1
        assert stats["pattern_usage"]["rest_api"] == 1
        assert "medium" in stats["complexity_distribution"]
        assert stats["average_generation_time"] > 0
    
    def test_get_generation_statistics(self, adapter_generator):
        """Test generation statistics retrieval"""
        # Add some mock stats
        adapter_generator.generation_stats["total_adapters_generated"] = 10
        adapter_generator.generation_stats["successful_generations"] = 8
        
        stats = adapter_generator.get_generation_statistics()
        
        assert "success_rate" in stats
        assert stats["success_rate"] == 0.8
        assert "supported_patterns" in stats
        assert len(stats["supported_patterns"]) == 7  # All adapter patterns
    
    def test_get_pattern_recommendations(self, adapter_generator, sample_project_state):
        """Test pattern recommendations"""
        recommendations = adapter_generator.get_pattern_recommendations(
            "Create REST API integration", sample_project_state
        )
        
        assert isinstance(recommendations, list)
        assert len(recommendations) > 0
        
        # Should be sorted by suitability score
        scores = [rec["suitability_score"] for rec in recommendations]
        assert scores == sorted(scores, reverse=True)
        
        # Top recommendation should be REST API for this goal
        top_rec = recommendations[0]
        assert top_rec["pattern"] == "rest_api"
        assert "description" in top_rec
        assert "complexity" in top_rec
        assert "required_capabilities" in top_rec
    
    def test_calculate_pattern_suitability(self, adapter_generator, sample_project_state):
        """Test pattern suitability calculation"""
        # Test REST API pattern with API-related goal
        score = adapter_generator._calculate_pattern_suitability(
            "Create REST API endpoint adapter",
            "rest_api",
            adapter_generator.adapter_patterns["rest_api"],
            sample_project_state
        )
        
        assert score > 0.5  # Should be highly suitable
        
        # Test database pattern with unrelated goal
        score = adapter_generator._calculate_pattern_suitability(
            "Create REST API endpoint adapter",
            "database",
            adapter_generator.adapter_patterns["database"],
            sample_project_state
        )
        
        assert score < 0.5  # Should be less suitable
