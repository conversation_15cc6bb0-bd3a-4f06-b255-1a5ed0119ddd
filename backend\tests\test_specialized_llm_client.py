"""
Tests for SpecializedLLMClient - Multi-model approach for optimal quality and cost.

Tests the intelligent model routing, task complexity assessment,
and cost optimization features.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from backend.src.modules.code_generation.specialized_llm_client import (
    SpecializedLLMClient, ModelTier, TaskComplexity
)
from backend.src.state.components import (
    GenerationContext, CodeGenerationType, ComponentAPI, IntegrationComplexity
)


class TestSpecializedLLMClient:
    """Test SpecializedLLMClient functionality"""
    
    @pytest.fixture
    def llm_client(self):
        """Create SpecializedLLMClient instance for testing"""
        return SpecializedLLMClient()
    
    @pytest.fixture
    def sample_generation_context(self):
        """Create sample generation context"""
        context = GenerationContext(
            goal="Create authentication adapter",
            generation_type=CodeGenerationType.ADAPTER,
            project_framework="fastapi",
            project_language="python"
        )
        
        # Add component APIs
        context.source_component_api = ComponentAPI(
            component_name="auth-service",
            language="python",
            exported_classes=[{"name": "AuthManager"}],
            public_methods=[{"name": "authenticate"}]
        )
        
        context.target_component_api = ComponentAPI(
            component_name="user-service", 
            language="python",
            exported_classes=[{"name": "UserService"}],
            public_methods=[{"name": "get_user"}]
        )
        
        return context
    
    def test_model_config_initialization(self, llm_client):
        """Test model configuration initialization"""
        config = llm_client.model_config
        
        # Verify essential models are configured
        assert "deepseek-coder" in config
        assert "claude-3-haiku" in config
        assert "claude-3-sonnet" in config
        assert "gpt-4" in config
        assert "gemini-pro" in config
        
        # Verify model structure
        for model_name, model_config in config.items():
            assert "tier" in model_config
            assert "capabilities" in model_config
            assert "cost_per_token" in model_config
            assert "max_tokens" in model_config
            assert "best_for" in model_config
            assert "strengths" in model_config
            assert "limitations" in model_config
    
    def test_model_tier_classification(self, llm_client):
        """Test model tier classification"""
        config = llm_client.model_config
        
        # Verify tier classifications
        assert config["deepseek-coder"]["tier"] == ModelTier.FAST
        assert config["claude-3-haiku"]["tier"] == ModelTier.BALANCED
        assert config["claude-3-sonnet"]["tier"] == ModelTier.PREMIUM
        assert config["gpt-4"]["tier"] == ModelTier.PREMIUM
        assert config["gemini-pro"]["tier"] == ModelTier.BALANCED
    
    def test_task_complexity_assessment_simple(self, llm_client):
        """Test task complexity assessment for simple tasks"""
        context = GenerationContext(
            goal="Simple configuration",
            generation_type=CodeGenerationType.CONFIGURATION,
            project_framework="fastapi",
            project_language="python"
        )
        
        complexity = llm_client._assess_task_complexity(context)
        assert complexity in [TaskComplexity.SIMPLE, TaskComplexity.MODERATE]
    
    def test_task_complexity_assessment_complex(self, llm_client, sample_generation_context):
        """Test task complexity assessment for complex tasks"""
        # Make it more complex
        sample_generation_context.generation_type = CodeGenerationType.PROJECT_STRUCTURE
        sample_generation_context.max_complexity = IntegrationComplexity.VERY_HIGH
        
        complexity = llm_client._assess_task_complexity(sample_generation_context)
        assert complexity in [TaskComplexity.COMPLEX, TaskComplexity.CRITICAL]
    
    def test_optimal_model_selection_adapter(self, llm_client, sample_generation_context):
        """Test optimal model selection for adapter generation"""
        model = llm_client._select_optimal_model(
            sample_generation_context, 
            preferred_model=None, 
            quality_level="production"
        )
        
        # Should select a model good for adapters
        config = llm_client.model_config[model]
        assert CodeGenerationType.ADAPTER in config.get("best_for", []) or \
               "code_generation" in config.get("capabilities", [])
    
    def test_optimal_model_selection_tests(self, llm_client):
        """Test optimal model selection for test generation"""
        context = GenerationContext(
            goal="Generate tests",
            generation_type=CodeGenerationType.TESTS,
            project_framework="fastapi",
            project_language="python"
        )
        
        model = llm_client._select_optimal_model(context, None, "production")
        
        # Should prefer models good at test generation
        config = llm_client.model_config[model]
        assert CodeGenerationType.TESTS in config.get("best_for", []) or \
               "test_generation" in config.get("capabilities", [])
    
    def test_optimal_model_selection_quality_levels(self, llm_client, sample_generation_context):
        """Test model selection based on quality levels"""
        # Enterprise quality should prefer premium models
        enterprise_model = llm_client._select_optimal_model(
            sample_generation_context, None, "enterprise"
        )
        enterprise_config = llm_client.model_config[enterprise_model]
        assert enterprise_config["tier"] in [ModelTier.PREMIUM, ModelTier.BALANCED]
        
        # Prototype quality should prefer fast models
        prototype_model = llm_client._select_optimal_model(
            sample_generation_context, None, "prototype"
        )
        prototype_config = llm_client.model_config[prototype_model]
        assert prototype_config["tier"] in [ModelTier.FAST, ModelTier.BALANCED]
    
    def test_preferred_model_override(self, llm_client, sample_generation_context):
        """Test that preferred model is honored when valid"""
        preferred = "claude-3-sonnet"
        model = llm_client._select_optimal_model(
            sample_generation_context, preferred, "production"
        )
        assert model == preferred
    
    def test_select_from_candidates(self, llm_client):
        """Test candidate selection logic"""
        candidates = ["claude-3-sonnet", "gpt-4", "nonexistent-model"]
        selected = llm_client._select_from_candidates(candidates, "test_task")
        
        # Should select from available candidates
        assert selected in ["claude-3-sonnet", "gpt-4"]
        assert selected in llm_client.model_config
    
    def test_select_from_candidates_empty(self, llm_client):
        """Test candidate selection with no valid candidates"""
        candidates = ["nonexistent-model-1", "nonexistent-model-2"]
        selected = llm_client._select_from_candidates(candidates, "test_task")
        
        # Should fall back to default
        assert selected == "claude-3-haiku"
    
    @pytest.mark.asyncio
    async def test_generate_code_success(self, llm_client, sample_generation_context):
        """Test successful code generation"""
        prompt = "Generate authentication adapter code"
        
        result = await llm_client.generate_code(
            sample_generation_context, prompt, quality_level="production"
        )
        
        # Verify result structure
        assert result["success"] is True
        assert "generated_code" in result
        assert "model_used" in result
        assert "task_complexity" in result
        assert "generation_time_seconds" in result
        assert "quality_level" in result
        assert "cost_estimate" in result
        
        # Verify code content
        assert len(result["generated_code"]) > 0
        assert result["quality_level"] == "production"
    
    @pytest.mark.asyncio
    async def test_generate_code_with_preferred_model(self, llm_client, sample_generation_context):
        """Test code generation with preferred model"""
        prompt = "Generate adapter code"
        preferred_model = "claude-3-sonnet"
        
        result = await llm_client.generate_code(
            sample_generation_context, prompt, preferred_model=preferred_model
        )
        
        assert result["model_used"] == preferred_model
    
    @pytest.mark.asyncio
    async def test_correct_code_success(self, llm_client, sample_generation_context):
        """Test successful code correction"""
        original_code = """
def broken_function():
    return undefined_variable
"""
        error_context = "NameError: name 'undefined_variable' is not defined"
        
        result = await llm_client.correct_code(
            original_code, error_context, sample_generation_context
        )
        
        # Verify correction result
        assert "corrected_code" in result
        assert "explanation" in result
        assert "model_used" in result
        assert "correction_type" in result
        assert result["correction_type"] == "error_correction"
    
    def test_generate_mock_code_fast(self, llm_client, sample_generation_context):
        """Test fast mock code generation"""
        code = llm_client._generate_mock_code_fast(sample_generation_context)
        
        assert isinstance(code, str)
        assert len(code) > 0
        assert "ComponentAdapter" in code or "adapter" in code.lower()
    
    def test_generate_mock_code_quality(self, llm_client, sample_generation_context):
        """Test quality mock code generation"""
        code = llm_client._generate_mock_code_quality(sample_generation_context)
        
        assert isinstance(code, str)
        assert len(code) > 0
        # Quality code should have more structure
        assert "class" in code or "def" in code
        assert len(code) > 100  # Should be more comprehensive
    
    def test_generate_mock_code_generic(self, llm_client, sample_generation_context):
        """Test generic mock code generation"""
        code = llm_client._generate_mock_code_generic(sample_generation_context)
        
        assert isinstance(code, str)
        assert len(code) > 0
        assert "Generated" in code
    
    def test_build_correction_prompt(self, llm_client, sample_generation_context):
        """Test correction prompt building"""
        original_code = "def test(): pass"
        error_context = "SyntaxError: invalid syntax"
        
        prompt = llm_client._build_correction_prompt(
            original_code, error_context, sample_generation_context
        )
        
        assert isinstance(prompt, str)
        assert original_code in prompt
        assert error_context in prompt
        assert "expert code debugger" in prompt.lower()
        assert sample_generation_context.generation_type.value in prompt
    
    @pytest.mark.asyncio
    async def test_fallback_generation(self, llm_client, sample_generation_context):
        """Test fallback generation when primary methods fail"""
        prompt = "Generate test code"
        
        result = await llm_client._fallback_generation(sample_generation_context, prompt)
        
        assert "success" in result
        assert "generated_code" in result
        assert "model_used" in result
        assert result["model_used"] == "fallback"
    
    def test_usage_tracking(self, llm_client):
        """Test usage statistics tracking"""
        model_name = "test-model"
        tokens_used = 1000
        generation_time = 2.5
        
        # Track usage
        llm_client._track_usage(model_name, tokens_used, generation_time)
        
        # Verify tracking
        stats = llm_client.usage_stats
        assert stats["total_requests"] == 1
        assert stats["total_tokens"] == tokens_used
        assert model_name in stats["model_usage"]
        assert stats["model_usage"][model_name]["requests"] == 1
        assert stats["model_usage"][model_name]["tokens"] == tokens_used
        assert stats["model_usage"][model_name]["total_time"] == generation_time
    
    def test_correction_tracking(self, llm_client):
        """Test correction attempt tracking"""
        model_name = "test-model"
        
        # Track successful correction
        llm_client._track_correction_attempt(model_name, True)
        
        # Track failed correction
        llm_client._track_correction_attempt(model_name, False)
        
        # Verify tracking
        stats = llm_client.performance_stats["model_success_rates"][model_name]
        assert stats["corrections"] == 2
        assert stats["successes"] == 1
    
    def test_cost_calculation(self, llm_client):
        """Test cost calculation"""
        model_name = "claude-3-sonnet"
        tokens_used = 1000
        
        cost = llm_client._calculate_cost(model_name, tokens_used)
        
        # Should be positive and reasonable
        assert cost > 0
        assert cost < 10  # Sanity check
        
        # Should match model configuration
        expected_cost = llm_client.model_config[model_name]["cost_per_token"] * tokens_used
        assert cost == expected_cost
    
    def test_get_usage_statistics(self, llm_client):
        """Test comprehensive usage statistics"""
        # Add some usage data
        llm_client._track_usage("test-model", 1000, 2.0)
        llm_client._track_correction_attempt("test-model", True)
        
        stats = llm_client.get_usage_statistics()
        
        # Verify structure
        assert "total_requests" in stats
        assert "total_tokens" in stats
        assert "total_cost_estimate" in stats
        assert "model_usage" in stats
        assert "average_response_times" in stats
        assert "correction_success_rates" in stats
        assert "cost_by_model" in stats
    
    def test_get_model_recommendations(self, llm_client, sample_generation_context):
        """Test model recommendations"""
        recommendations = llm_client.get_model_recommendations(sample_generation_context)
        
        # Should return list of recommendations
        assert isinstance(recommendations, list)
        assert len(recommendations) > 0
        
        # Verify recommendation structure
        for rec in recommendations:
            assert "model" in rec
            assert "suitability_score" in rec
            assert "tier" in rec
            assert "estimated_cost" in rec
            assert "strengths" in rec
            assert "limitations" in rec
        
        # Should be sorted by suitability score
        scores = [rec["suitability_score"] for rec in recommendations]
        assert scores == sorted(scores, reverse=True)
    
    def test_get_required_capabilities(self, llm_client, sample_generation_context):
        """Test required capabilities determination"""
        capabilities = llm_client._get_required_capabilities(sample_generation_context)
        
        assert isinstance(capabilities, list)
        assert len(capabilities) > 0
        
        # Should include relevant capabilities for adapter generation
        assert any("code_generation" in cap or "integration" in cap for cap in capabilities)
    
    def test_get_required_capabilities_complex(self, llm_client, sample_generation_context):
        """Test required capabilities for complex tasks"""
        sample_generation_context.max_complexity = IntegrationComplexity.VERY_HIGH
        
        capabilities = llm_client._get_required_capabilities(sample_generation_context)
        
        # Should include complex reasoning for high complexity tasks
        assert "complex_reasoning" in capabilities
