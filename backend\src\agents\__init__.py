"""
TaskExecutionAgent module for CodeQuilter.

This module implements the transparent task-based execution system that transforms
CodeQuilter from a "black box" AI tool into a transparent, trustworthy development partner.

Key Components:
- Task data models with lifecycle management
- TaskExecutionAgent with Plan-Do-Verify loop
- Real-time WebSocket communication
- Module integration wrappers
"""

from .task_models import Task, TaskStatus, ExecutionResult, SuggestedAction
from .task_execution_agent import TaskExecutionAgent

__all__ = [
    "Task",
    "TaskStatus", 
    "ExecutionResult",
    "SuggestedAction",
    "TaskExecutionAgent"
]
