"""
Specialized LLM Client - Multi-model approach for optimal quality and cost.

Implements intelligent model routing based on task complexity and requirements:
- Simple generation: Fast, cost-effective models (DeepSeek Coder)
- Complex reasoning: Powerful, expensive models (Claude 3.5 Sonnet)
- Error correction: Models with strong debugging capabilities
- Architecture decisions: Models excellent at system design

This is the intelligence layer that optimizes LLM usage for different code generation tasks.
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime
from enum import Enum

from ...state.components import CodeGenerationType, GenerationContext
from ...integrations.llm_client import LLMClient

logger = logging.getLogger(__name__)


class ModelTier(Enum):
    """Model tiers based on capability and cost"""
    FAST = "fast"           # Fast, cost-effective models
    BALANCED = "balanced"   # Good balance of quality and cost
    PREMIUM = "premium"     # High-quality, expensive models
    SPECIALIZED = "specialized"  # Task-specific specialized models


class TaskComplexity(Enum):
    """Task complexity levels for model selection"""
    SIMPLE = "simple"       # Basic code generation
    MODERATE = "moderate"   # Standard integration tasks
    COMPLEX = "complex"     # Advanced reasoning required
    CRITICAL = "critical"   # Mission-critical, highest quality needed


class SpecializedLLMClient:
    """
    Multi-model LLM client with intelligent routing for code generation tasks.
    
    Routes requests to appropriate models based on task type, complexity,
    and quality requirements to optimize both output quality and cost.
    """
    
    def __init__(self):
        """Initialize the specialized LLM client with model configurations"""
        # TODO: REPLACE_MOCK - Real LLM client integration
        self.base_client = LLMClient()
        
        # Model configuration mapping
        self.model_config = self._initialize_model_config()
        
        # Usage tracking for cost optimization
        self.usage_stats = {
            "total_requests": 0,
            "total_tokens": 0,
            "model_usage": {},
            "cost_tracking": {}
        }
        
        # Performance tracking for model selection optimization
        self.performance_stats = {
            "model_success_rates": {},
            "average_response_times": {},
            "quality_scores": {}
        }
    
    def _initialize_model_config(self) -> Dict[str, Any]:
        """Initialize model configuration with capabilities and costs"""
        return {
            # Fast, cost-effective models
            "deepseek-coder": {
                "tier": ModelTier.FAST,
                "capabilities": ["code_generation", "simple_debugging"],
                "cost_per_token": 0.0001,
                "max_tokens": 4096,
                "best_for": [CodeGenerationType.ADAPTER, CodeGenerationType.CONFIGURATION],
                "strengths": ["fast_generation", "code_syntax", "simple_patterns"],
                "limitations": ["complex_reasoning", "architecture_design"]
            },
            
            "claude-3-haiku": {
                "tier": ModelTier.BALANCED,
                "capabilities": ["code_generation", "test_generation", "documentation"],
                "cost_per_token": 0.0003,
                "max_tokens": 8192,
                "best_for": [CodeGenerationType.TESTS, CodeGenerationType.DOCUMENTATION],
                "strengths": ["test_generation", "clear_explanations", "good_balance"],
                "limitations": ["very_complex_architecture"]
            },
            
            "claude-3-sonnet": {
                "tier": ModelTier.PREMIUM,
                "capabilities": ["complex_reasoning", "architecture_design", "error_correction"],
                "cost_per_token": 0.001,
                "max_tokens": 16384,
                "best_for": [CodeGenerationType.PROJECT_STRUCTURE, "error_correction"],
                "strengths": ["complex_reasoning", "architecture", "debugging"],
                "limitations": ["cost", "slower_response"]
            },
            
            "gpt-4": {
                "tier": ModelTier.PREMIUM,
                "capabilities": ["complex_reasoning", "architecture_design", "code_review"],
                "cost_per_token": 0.0015,
                "max_tokens": 8192,
                "best_for": ["architecture_decisions", "complex_debugging"],
                "strengths": ["reasoning", "problem_solving", "code_quality"],
                "limitations": ["cost", "token_limit"]
            },
            
            # TODO: REPLACE_MOCK - Add real model configurations
            "gemini-pro": {
                "tier": ModelTier.BALANCED,
                "capabilities": ["code_generation", "reasoning", "multimodal"],
                "cost_per_token": 0.0002,
                "max_tokens": 12288,
                "best_for": [CodeGenerationType.ADAPTER, CodeGenerationType.TESTS],
                "strengths": ["versatile", "good_reasoning", "cost_effective"],
                "limitations": ["newer_model", "less_specialized"]
            }
        }
    
    async def generate_code(
        self,
        context: GenerationContext,
        prompt: str,
        preferred_model: Optional[str] = None,
        quality_level: str = "production"
    ) -> Dict[str, Any]:
        """
        Generate code using the most appropriate model for the task.
        
        Args:
            context: Generation context with task details
            prompt: Optimized prompt for code generation
            preferred_model: User's preferred model (optional)
            quality_level: Required quality level (prototype, production, enterprise)
            
        Returns:
            Dictionary with generated code and metadata
        """
        logger.info(f"Generating {context.generation_type.value} code with quality level: {quality_level}")
        
        try:
            # Select optimal model
            selected_model = self._select_optimal_model(context, preferred_model, quality_level)
            
            # Assess task complexity
            complexity = self._assess_task_complexity(context)
            
            # Generate code with selected model
            start_time = datetime.now()
            result = await self._generate_with_model(selected_model, prompt, context)
            generation_time = (datetime.now() - start_time).total_seconds()
            
            # Track usage and performance
            self._track_usage(selected_model, result.get("token_usage", 0), generation_time)
            
            # Enhance result with metadata
            enhanced_result = {
                **result,
                "model_used": selected_model,
                "task_complexity": complexity.value,
                "generation_time_seconds": generation_time,
                "quality_level": quality_level,
                "cost_estimate": self._calculate_cost(selected_model, result.get("token_usage", 0))
            }
            
            logger.info(f"Code generation complete using {selected_model} in {generation_time:.2f}s")
            return enhanced_result
            
        except Exception as e:
            logger.error(f"Error in code generation: {e}")
            # Fallback to basic generation
            return await self._fallback_generation(context, prompt)
    
    async def correct_code(
        self,
        original_code: str,
        error_context: str,
        generation_context: GenerationContext
    ) -> Dict[str, Any]:
        """
        Correct code using models specialized in error correction and debugging.
        
        Args:
            original_code: The code that needs correction
            error_context: Error messages and context
            generation_context: Original generation context
            
        Returns:
            Dictionary with corrected code and explanation
        """
        logger.info("Performing code correction with specialized model")
        
        try:
            # Use premium models for error correction (better reasoning)
            correction_models = ["claude-3-sonnet", "gpt-4"]
            selected_model = self._select_from_candidates(correction_models, "error_correction")
            
            # Build correction prompt
            correction_prompt = self._build_correction_prompt(original_code, error_context, generation_context)
            
            # Generate correction
            result = await self._generate_with_model(selected_model, correction_prompt, generation_context)
            
            # Track correction attempt
            self._track_correction_attempt(selected_model, result.get("success", False))
            
            return {
                **result,
                "corrected_code": result.get("generated_code", original_code),
                "explanation": "Code corrected using specialized model",
                "model_used": selected_model,
                "correction_type": "error_correction",
                "original_code_length": len(original_code),
                "error_context": error_context
            }
            
        except Exception as e:
            logger.error(f"Error in code correction: {e}")
            return {
                "success": False,
                "error": str(e),
                "corrected_code": original_code,  # Return original as fallback
                "explanation": "Code correction failed, returning original code"
            }
    
    def _select_optimal_model(
        self,
        context: GenerationContext,
        preferred_model: Optional[str],
        quality_level: str
    ) -> str:
        """Select the optimal model based on context and requirements"""
        
        # Honor user preference if valid
        if preferred_model and preferred_model in self.model_config:
            logger.info(f"Using user-preferred model: {preferred_model}")
            return preferred_model
        
        # Auto-select based on generation type and quality level
        if context.preferred_model != "auto":
            if context.preferred_model in self.model_config:
                return context.preferred_model
        
        # Task-specific model selection
        task_models = {
            CodeGenerationType.ADAPTER: ["deepseek-coder", "gemini-pro"],
            CodeGenerationType.TESTS: ["claude-3-haiku", "gemini-pro"],
            CodeGenerationType.CONFIGURATION: ["deepseek-coder", "claude-3-haiku"],
            CodeGenerationType.DOCUMENTATION: ["claude-3-haiku", "claude-3-sonnet"],
            CodeGenerationType.PROJECT_STRUCTURE: ["claude-3-sonnet", "gpt-4"]
        }
        
        # Quality level adjustments
        if quality_level == "enterprise":
            # Prefer premium models for enterprise quality
            candidates = ["claude-3-sonnet", "gpt-4"]
        elif quality_level == "prototype":
            # Prefer fast, cost-effective models for prototyping
            candidates = ["deepseek-coder", "gemini-pro"]
        else:  # production
            # Use task-specific recommendations
            candidates = task_models.get(context.generation_type, ["claude-3-haiku"])
        
        # Select best available candidate
        return self._select_from_candidates(candidates, context.generation_type.value)
    
    def _select_from_candidates(self, candidates: List[str], task_type: str) -> str:
        """Select the best model from a list of candidates"""
        # Filter available models
        available_candidates = [model for model in candidates if model in self.model_config]
        
        if not available_candidates:
            logger.warning(f"No available models for {task_type}, using default")
            return "claude-3-haiku"  # Safe default
        
        # TODO: REPLACE_MOCK - Implement intelligent selection based on:
        # - Current model availability
        # - Performance statistics
        # - Cost constraints
        # - Response time requirements
        
        # For now, return the first available candidate
        selected = available_candidates[0]
        logger.info(f"Selected model {selected} for {task_type}")
        return selected

    def _assess_task_complexity(self, context: GenerationContext) -> TaskComplexity:
        """Assess the complexity of the generation task"""
        complexity_score = 0

        # Component complexity
        if context.source_component_api and context.target_component_api:
            complexity_score += 0.3  # Integration between two components
        elif context.source_component_api or context.target_component_api:
            complexity_score += 0.1  # Single component analysis

        # Generation type complexity
        type_complexity = {
            CodeGenerationType.CONFIGURATION: 0.1,
            CodeGenerationType.ADAPTER: 0.3,
            CodeGenerationType.TESTS: 0.2,
            CodeGenerationType.DOCUMENTATION: 0.1,
            CodeGenerationType.PROJECT_STRUCTURE: 0.4
        }
        complexity_score += type_complexity.get(context.generation_type, 0.2)

        # Context richness (more context = potentially more complex)
        complexity_score += context.get_context_richness_score() * 0.2

        # Map to complexity enum
        if complexity_score < 0.2:
            return TaskComplexity.SIMPLE
        elif complexity_score < 0.4:
            return TaskComplexity.MODERATE
        elif complexity_score < 0.7:
            return TaskComplexity.COMPLEX
        else:
            return TaskComplexity.CRITICAL

    async def _generate_with_model(
        self,
        model_name: str,
        prompt: str,
        context: GenerationContext
    ) -> Dict[str, Any]:
        """Generate code using a specific model"""
        # TODO: REPLACE_MOCK - Real model-specific generation

        model_config = self.model_config.get(model_name, {})
        max_tokens = model_config.get("max_tokens", 4096)

        # Simulate model-specific behavior
        if "deepseek" in model_name.lower():
            # Fast, code-focused generation
            generated_code = self._generate_mock_code_fast(context)
            token_usage = len(prompt.split()) + len(generated_code.split())
        elif "claude" in model_name.lower():
            # High-quality, well-reasoned generation
            generated_code = self._generate_mock_code_quality(context)
            token_usage = len(prompt.split()) + len(generated_code.split())
        else:
            # Generic generation
            generated_code = self._generate_mock_code_generic(context)
            token_usage = len(prompt.split()) + len(generated_code.split())

        return {
            "success": True,
            "generated_code": generated_code,
            "token_usage": min(token_usage, max_tokens),
            "model_response": {
                "model": model_name,
                "finish_reason": "completed",
                "usage": {"total_tokens": token_usage}
            }
        }

    def _generate_mock_code_fast(self, context: GenerationContext) -> str:
        """Generate mock code optimized for speed (DeepSeek style)"""
        if context.generation_type == CodeGenerationType.ADAPTER:
            return '''
# Fast adapter generation
class ComponentAdapter:
    def __init__(self, source, target):
        self.source = source
        self.target = target

    def adapt(self, data):
        # Quick data transformation
        return self.target.process(data)
'''
        elif context.generation_type == CodeGenerationType.CONFIGURATION:
            return '''
# Configuration setup
config = {
    "database_url": "sqlite:///app.db",
    "debug": False,
    "secret_key": "your-secret-key"
}
'''
        else:
            return f"# Generated {context.generation_type.value} code\n# Fast generation placeholder"

    def _generate_mock_code_quality(self, context: GenerationContext) -> str:
        """Generate mock code optimized for quality (Claude style)"""
        if context.generation_type == CodeGenerationType.ADAPTER:
            return '''
"""
High-quality adapter implementation with comprehensive error handling.
"""
import logging
from typing import Any, Optional, Dict
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class AdapterConfig:
    """Configuration for the component adapter."""
    timeout: int = 30
    retry_attempts: int = 3
    enable_logging: bool = True

class ComponentAdapter:
    """
    Robust adapter between source and target components.

    Provides data transformation, error handling, and monitoring
    capabilities for seamless component integration.
    """

    def __init__(self, source, target, config: Optional[AdapterConfig] = None):
        self.source = source
        self.target = target
        self.config = config or AdapterConfig()

        if self.config.enable_logging:
            logger.info(f"Initialized adapter: {source} -> {target}")

    async def adapt(self, data: Any) -> Dict[str, Any]:
        """
        Adapt data from source to target format.

        Args:
            data: Input data from source component

        Returns:
            Transformed data compatible with target component

        Raises:
            AdapterError: If transformation fails
        """
        try:
            # Validate input data
            if not self._validate_input(data):
                raise ValueError("Invalid input data format")

            # Transform data
            transformed_data = self._transform_data(data)

            # Process with target component
            result = await self.target.process(transformed_data)

            logger.info("Adapter operation completed successfully")
            return result

        except Exception as e:
            logger.error(f"Adapter operation failed: {e}")
            raise AdapterError(f"Failed to adapt data: {e}") from e

    def _validate_input(self, data: Any) -> bool:
        """Validate input data format."""
        # Implementation would depend on specific requirements
        return data is not None

    def _transform_data(self, data: Any) -> Any:
        """Transform data to target format."""
        # Implementation would depend on source/target schemas
        return data

class AdapterError(Exception):
    """Custom exception for adapter operations."""
    pass
'''
        else:
            return f'''
"""
High-quality {context.generation_type.value} implementation.

This code follows best practices for maintainability,
error handling, and documentation.
"""

# Quality implementation placeholder
# Generated with comprehensive error handling and logging
'''

    def _generate_mock_code_generic(self, context: GenerationContext) -> str:
        """Generate generic mock code"""
        return f'''
# Generated {context.generation_type.value} code
# Generic implementation

class Generated{context.generation_type.value.title()}:
    def __init__(self):
        self.initialized = True

    def execute(self):
        return "Generated code executed successfully"
'''

    def _build_correction_prompt(
        self,
        original_code: str,
        error_context: str,
        generation_context: GenerationContext
    ) -> str:
        """Build a prompt for code correction"""
        return f"""You are an expert code debugger. Please analyze and fix the following code.

ORIGINAL CODE:
```
{original_code}
```

ERROR CONTEXT:
{error_context}

GENERATION CONTEXT:
- Task: {generation_context.generation_type.value}
- Framework: {generation_context.project_framework}
- Language: {generation_context.project_language}

Please provide:
1. Corrected code that fixes the identified issues
2. Explanation of what was wrong and how it was fixed
3. Suggestions to prevent similar issues in the future

Focus on maintaining the original intent while ensuring the code is correct, secure, and follows best practices."""

    async def _fallback_generation(self, context: GenerationContext, prompt: str) -> Dict[str, Any]:
        """Fallback generation when primary methods fail"""
        logger.warning("Using fallback generation due to primary method failure")

        try:
            # Use the base LLM client as fallback
            result = await self.base_client.generate_code(prompt)

            return {
                "success": True,
                "generated_code": result.data.get("generated_code", "# Fallback generation"),
                "model_used": "fallback",
                "token_usage": result.data.get("tokens_used", 0),
                "warning": "Generated using fallback method"
            }
        except Exception as e:
            logger.error(f"Fallback generation also failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "generated_code": f"# Error: Could not generate {context.generation_type.value} code",
                "model_used": "none"
            }

    def _track_usage(self, model_name: str, tokens_used: int, generation_time: float):
        """Track model usage for optimization"""
        self.usage_stats["total_requests"] += 1
        self.usage_stats["total_tokens"] += tokens_used

        if model_name not in self.usage_stats["model_usage"]:
            self.usage_stats["model_usage"][model_name] = {
                "requests": 0,
                "tokens": 0,
                "total_time": 0.0
            }

        model_stats = self.usage_stats["model_usage"][model_name]
        model_stats["requests"] += 1
        model_stats["tokens"] += tokens_used
        model_stats["total_time"] += generation_time

        # Track cost
        model_config = self.model_config.get(model_name, {})
        cost_per_token = model_config.get("cost_per_token", 0.0001)
        cost = tokens_used * cost_per_token

        if model_name not in self.usage_stats["cost_tracking"]:
            self.usage_stats["cost_tracking"][model_name] = 0.0
        self.usage_stats["cost_tracking"][model_name] += cost

    def _track_correction_attempt(self, model_name: str, success: bool):
        """Track code correction attempts for model performance analysis"""
        if model_name not in self.performance_stats["model_success_rates"]:
            self.performance_stats["model_success_rates"][model_name] = {
                "corrections": 0,
                "successes": 0
            }

        stats = self.performance_stats["model_success_rates"][model_name]
        stats["corrections"] += 1
        if success:
            stats["successes"] += 1

    def _calculate_cost(self, model_name: str, tokens_used: int) -> float:
        """Calculate estimated cost for the generation"""
        model_config = self.model_config.get(model_name, {})
        cost_per_token = model_config.get("cost_per_token", 0.0001)
        return tokens_used * cost_per_token

    def get_usage_statistics(self) -> Dict[str, Any]:
        """Get comprehensive usage statistics"""
        total_cost = sum(self.usage_stats["cost_tracking"].values())

        # Calculate average response times
        avg_response_times = {}
        for model, stats in self.usage_stats["model_usage"].items():
            if stats["requests"] > 0:
                avg_response_times[model] = stats["total_time"] / stats["requests"]

        # Calculate success rates
        success_rates = {}
        for model, stats in self.performance_stats["model_success_rates"].items():
            if stats["corrections"] > 0:
                success_rates[model] = stats["successes"] / stats["corrections"]

        return {
            "total_requests": self.usage_stats["total_requests"],
            "total_tokens": self.usage_stats["total_tokens"],
            "total_cost_estimate": total_cost,
            "model_usage": self.usage_stats["model_usage"],
            "average_response_times": avg_response_times,
            "correction_success_rates": success_rates,
            "cost_by_model": self.usage_stats["cost_tracking"]
        }

    def get_model_recommendations(self, context: GenerationContext) -> List[Dict[str, Any]]:
        """Get model recommendations for a given context"""
        recommendations = []

        for model_name, config in self.model_config.items():
            # Calculate suitability score
            suitability_score = 0.0

            # Check if model is good for this generation type
            if context.generation_type in config.get("best_for", []):
                suitability_score += 0.4

            # Check capabilities match
            required_capabilities = self._get_required_capabilities(context)
            model_capabilities = config.get("capabilities", [])
            capability_match = len(set(required_capabilities) & set(model_capabilities))
            suitability_score += (capability_match / max(len(required_capabilities), 1)) * 0.3

            # Factor in cost (lower cost = higher score for cost-sensitive tasks)
            cost_factor = 1.0 - min(config.get("cost_per_token", 0.001) / 0.002, 1.0)
            suitability_score += cost_factor * 0.2

            # Factor in performance history
            if model_name in self.performance_stats["model_success_rates"]:
                success_rate = self.performance_stats["model_success_rates"][model_name]
                if success_rate["corrections"] > 0:
                    rate = success_rate["successes"] / success_rate["corrections"]
                    suitability_score += rate * 0.1

            recommendations.append({
                "model": model_name,
                "suitability_score": suitability_score,
                "tier": config["tier"].value,
                "estimated_cost": self._calculate_cost(model_name, 1000),  # Cost per 1k tokens
                "strengths": config.get("strengths", []),
                "limitations": config.get("limitations", [])
            })

        # Sort by suitability score
        recommendations.sort(key=lambda x: x["suitability_score"], reverse=True)
        return recommendations

    def _get_required_capabilities(self, context: GenerationContext) -> List[str]:
        """Get required capabilities based on generation context"""
        capabilities = []

        if context.generation_type == CodeGenerationType.ADAPTER:
            capabilities.extend(["code_generation", "integration_patterns"])
        elif context.generation_type == CodeGenerationType.TESTS:
            capabilities.extend(["test_generation", "code_analysis"])
        elif context.generation_type == CodeGenerationType.DOCUMENTATION:
            capabilities.extend(["documentation", "clear_explanations"])
        elif context.generation_type == CodeGenerationType.PROJECT_STRUCTURE:
            capabilities.extend(["architecture_design", "complex_reasoning"])
        else:
            capabilities.append("code_generation")

        # Add complexity-based requirements
        if context.max_complexity:
            if hasattr(context.max_complexity, 'value'):
                complexity_value = context.max_complexity.value
            else:
                complexity_value = str(context.max_complexity)

            if complexity_value.upper() in ["HIGH", "VERY_HIGH"] or complexity_value in ["high", "very_high"]:
                capabilities.append("complex_reasoning")

        return capabilities
