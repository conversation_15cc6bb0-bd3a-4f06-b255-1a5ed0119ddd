"""
Comprehensive failure scenario tests for TaskExecutionAgent.

Tests deliberate failure injection to ensure robust error handling,
proper stop-on-failure behavior, and appropriate user feedback.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock

from backend.src.agents.task_execution_agent import TaskExecutionAgent
from backend.src.agents.task_models import Task, TaskStatus, ExecutionResult
from backend.src.state.project_state import ProjectState, ProjectStatus
from backend.src.api.websocket import TaskExecutionWebSocketManager


class TestTaskExecutionFailureScenarios:
    """Test comprehensive failure scenarios for TaskExecutionAgent"""
    
    @pytest.fixture
    def agent(self):
        """Create a TaskExecutionAgent for testing"""
        return TaskExecutionAgent()
    
    @pytest.fixture
    def project_state(self):
        """Create a ProjectState for testing"""
        return ProjectState(
            session_id="test_session",
            project_brief="Test project for failure scenarios",
            status=ProjectStatus.QUILTING
        )
    
    @pytest.mark.asyncio
    async def test_plan_generation_failure(self, agent):
        """Test failure during plan generation"""
        # Mock the plan method itself to fail
        original_plan = agent.plan
        async def failing_plan(goal, context):
            raise Exception("LLM service unavailable")

        agent.plan = failing_plan

        with pytest.raises(Exception) as exc_info:
            await agent.plan("test goal", {})

        assert "LLM service unavailable" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_first_task_failure_stops_execution(self, agent, project_state):
        """Test that failure in first task stops entire execution"""
        tasks = [
            Task(description="Failing first task"),
            Task(description="Should not execute"),
            Task(description="Should not execute either")
        ]
        
        # Mock first task to fail
        original_execute = agent._execute_single_task
        async def mock_execute(task, state):
            if "Failing first task" in task.description:
                task.fail("Simulated first task failure")
                return False
            return await original_execute(task, state)
        
        agent._execute_single_task = mock_execute
        
        result = await agent.execute(tasks, project_state)
        
        # Verify stop-on-failure behavior
        assert result.success is False
        assert len(result.completed_tasks) == 0  # No tasks completed
        assert result.failed_task is not None
        assert result.failed_task.description == "Failing first task"
        assert result.failed_task.status == TaskStatus.FAILED
        assert "Simulated first task failure" in result.failed_task.error_message
        
        # Verify remaining tasks were not executed
        assert tasks[1].status == TaskStatus.PENDING
        assert tasks[2].status == TaskStatus.PENDING
    
    @pytest.mark.asyncio
    async def test_middle_task_failure_stops_execution(self, agent, project_state):
        """Test that failure in middle task stops execution"""
        tasks = [
            Task(description="Should complete"),
            Task(description="Failing middle task"),
            Task(description="Should not execute")
        ]
        
        # Mock middle task to fail
        original_execute = agent._execute_single_task
        async def mock_execute(task, state):
            if "Failing middle task" in task.description:
                task.fail("Simulated middle task failure")
                return False
            return await original_execute(task, state)
        
        agent._execute_single_task = mock_execute
        
        result = await agent.execute(tasks, project_state)
        
        # Verify stop-on-failure behavior
        assert result.success is False
        assert len(result.completed_tasks) == 1  # Only first task completed
        assert result.completed_tasks[0].description == "Should complete"
        assert result.failed_task.description == "Failing middle task"
        
        # Verify last task was not executed
        assert tasks[2].status == TaskStatus.PENDING
    
    @pytest.mark.asyncio
    async def test_timeout_failure_scenario(self, agent, project_state):
        """Test timeout failure handling"""
        tasks = [Task(description="Timeout task")]
        
        # Mock task to timeout
        original_execute = agent._execute_single_task
        async def mock_timeout_execute(task, state):
            task.update_activity("Starting long operation...")
            await asyncio.sleep(0.1)  # Simulate some work
            task.fail("Operation timed out after 30 seconds")
            return False
        
        agent._execute_single_task = mock_timeout_execute
        
        result = await agent.execute(tasks, project_state)
        
        assert result.success is False
        assert result.failed_task.error_message == "Operation timed out after 30 seconds"
        
        # Verify timeout failures don't suggest retry
        suggested_actions = result.get_failure_actions(allow_retry=False)
        action_types = [action.action_type.value for action in suggested_actions]
        assert "retry_plan" not in action_types
    
    @pytest.mark.asyncio
    async def test_network_failure_scenario(self, agent, project_state):
        """Test network failure handling"""
        tasks = [Task(description="Network dependent task")]
        
        # Mock network failure
        original_execute = agent._execute_single_task
        async def mock_network_failure(task, state):
            task.update_activity("Connecting to external service...")
            await asyncio.sleep(0.05)
            task.fail("Network connection failed: Connection timeout")
            return False
        
        agent._execute_single_task = mock_network_failure
        
        result = await agent.execute(tasks, project_state)
        
        assert result.success is False
        assert "Network connection failed" in result.failed_task.error_message
        
        # Network failures should allow retry
        suggested_actions = result.get_failure_actions(allow_retry=True)
        action_types = [action.action_type.value for action in suggested_actions]
        assert "retry_plan" in action_types
    
    @pytest.mark.asyncio
    async def test_resource_not_found_failure(self, agent, project_state):
        """Test resource not found failure (should not allow retry)"""
        tasks = [Task(description="Find missing component")]
        
        # Mock resource not found
        original_execute = agent._execute_single_task
        async def mock_not_found(task, state):
            task.update_activity("Searching for component...")
            await asyncio.sleep(0.05)
            task.fail("Component not found in any repository")
            return False
        
        agent._execute_single_task = mock_not_found
        
        result = await agent.execute(tasks, project_state)
        
        assert result.success is False
        assert "not found" in result.failed_task.error_message.lower()
        
        # "Not found" failures should not allow retry
        suggested_actions = result.get_failure_actions(allow_retry=False)
        action_types = [action.action_type.value for action in suggested_actions]
        assert "retry_plan" not in action_types
        assert "edit_goal_replan" in action_types
    
    @pytest.mark.asyncio
    async def test_unexpected_exception_handling(self, agent, project_state):
        """Test handling of unexpected exceptions"""
        tasks = [Task(description="Task with unexpected error")]
        
        # Mock unexpected exception
        original_execute = agent._execute_single_task
        async def mock_unexpected_error(task, state):
            task.update_activity("Processing...")
            raise RuntimeError("Unexpected system error")
        
        agent._execute_single_task = mock_unexpected_error
        
        result = await agent.execute(tasks, project_state)
        
        assert result.success is False
        assert result.failed_task is not None
        assert "Unexpected system error" in result.failed_task.error_message
        assert result.failed_task.status == TaskStatus.FAILED
    
    @pytest.mark.asyncio
    async def test_activity_callback_failure_resilience(self, agent, project_state):
        """Test that activity callback failures don't break execution"""
        tasks = [Task(description="Task with failing callback")]
        
        # Set up failing activity callback
        def failing_callback(task_id, activity):
            raise Exception("Callback system failure")
        
        agent.set_activity_callback(failing_callback)
        
        # Execution should still succeed despite callback failures
        result = await agent.execute(tasks, project_state)
        
        assert result.success is True  # Execution should succeed
        assert len(result.completed_tasks) == 1
    
    @pytest.mark.asyncio
    async def test_project_state_corruption_handling(self, agent):
        """Test handling of corrupted project state"""
        # Create corrupted project state
        corrupted_state = Mock()
        corrupted_state.set_active_task_list = Mock(side_effect=Exception("State corruption"))
        corrupted_state.update_task_in_list = Mock(side_effect=Exception("State corruption"))
        corrupted_state.clear_active_task_list = Mock(side_effect=Exception("State corruption"))
        
        tasks = [Task(description="Task with corrupted state")]
        
        # Execution should handle state corruption gracefully
        result = await agent.execute(tasks, corrupted_state)
        
        # Should fail but not crash
        assert result.success is False
        assert result.failed_task is not None
    
    @pytest.mark.asyncio
    async def test_memory_pressure_simulation(self, agent, project_state):
        """Test behavior under simulated memory pressure"""
        # Create many tasks to simulate memory pressure
        tasks = [Task(description=f"Memory intensive task {i}") for i in range(100)]
        
        # Mock memory pressure failure
        original_execute = agent._execute_single_task
        call_count = 0
        async def mock_memory_pressure(task, state):
            nonlocal call_count
            call_count += 1
            if call_count > 50:  # Fail after 50 tasks
                task.fail("Out of memory: Cannot allocate additional resources")
                return False
            return await original_execute(task, state)
        
        agent._execute_single_task = mock_memory_pressure
        
        result = await agent.execute(tasks, project_state)
        
        assert result.success is False
        assert len(result.completed_tasks) == 50
        assert "Out of memory" in result.failed_task.error_message


class TestWebSocketFailureScenarios:
    """Test WebSocket failure scenarios"""
    
    @pytest.fixture
    def manager(self):
        """Create a WebSocket manager for testing"""
        return TaskExecutionWebSocketManager()
    
    @pytest.fixture
    def mock_websocket(self):
        """Create a mock WebSocket for testing"""
        from fastapi.websockets import WebSocketState
        websocket = Mock()
        websocket.client_state = WebSocketState.CONNECTED
        websocket.accept = AsyncMock()
        websocket.send_text = AsyncMock()
        websocket.receive_text = AsyncMock()
        return websocket
    
    @pytest.mark.asyncio
    async def test_websocket_connection_failure(self, manager, mock_websocket):
        """Test WebSocket connection failure handling"""
        mock_websocket.accept = AsyncMock(side_effect=Exception("Connection failed"))
        
        with pytest.raises(Exception):
            await manager.connect(mock_websocket, "test_session")
    
    @pytest.mark.asyncio
    async def test_websocket_send_failure(self, manager, mock_websocket):
        """Test WebSocket send failure handling"""
        await manager.connect(mock_websocket, "test_session")
        
        # Mock send failure
        mock_websocket.send_text = AsyncMock(side_effect=Exception("Send failed"))
        
        # Should handle send failure gracefully
        await manager._send_message("test_session", {"type": "TEST", "data": "test"})
        
        # Connection should be cleaned up
        assert "test_session" not in manager.active_connections
    
    @pytest.mark.asyncio
    async def test_plan_generation_websocket_failure(self, manager, mock_websocket):
        """Test plan generation failure over WebSocket"""
        await manager.connect(mock_websocket, "test_session")
        
        # Mock agent to fail
        mock_agent = manager.task_agents["test_session"]
        mock_agent.plan = AsyncMock(side_effect=Exception("Plan generation failed"))
        
        await manager.handle_plan_request("test_session", "test goal", {})
        
        # Should send error message
        error_sent = False
        for call in mock_websocket.send_text.call_args_list:
            message = eval(call[0][0])  # Parse JSON-like string
            if message.get("type") == "ERROR":
                error_sent = True
                assert "Plan generation failed" in message["error"]
                break
        
        assert error_sent


class TestErrorRecoveryScenarios:
    """Test error recovery and resilience scenarios"""
    
    @pytest.mark.asyncio
    async def test_partial_success_recovery(self):
        """Test recovery from partial success scenarios"""
        agent = TaskExecutionAgent()
        project_state = ProjectState(session_id="test")
        
        tasks = [
            Task(description="Success task 1"),
            Task(description="Success task 2"),
            Task(description="Failure task"),
            Task(description="Never executed")
        ]
        
        # Mock partial failure
        original_execute = agent._execute_single_task
        async def mock_partial_failure(task, state):
            if "Failure task" in task.description:
                task.fail("Simulated failure for testing")
                return False
            return await original_execute(task, state)
        
        agent._execute_single_task = mock_partial_failure
        
        result = await agent.execute(tasks, project_state)
        
        # Verify partial success is properly reported
        assert result.success is False
        assert len(result.completed_tasks) == 2
        assert result.failed_task.description == "Failure task"
        assert result.total_duration_seconds > 0
        
        # Verify appropriate recovery suggestions
        suggested_actions = result.get_failure_actions(allow_retry=True)
        assert len(suggested_actions) >= 2
        action_types = [action.action_type.value for action in suggested_actions]
        assert "retry_plan" in action_types
        assert "edit_goal_replan" in action_types
