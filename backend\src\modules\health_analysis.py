"""
Health Analysis Engine - Quantifiable repository health scoring.

Implements transparent scoring algorithm:
- Activity (40%): Recent commits and maintenance status
- Popularity (30%): Stars, forks, community engagement  
- License (20%): Compatibility with project requirements
- Security (10%): Known vulnerabilities and issues

TODO: IMPLEMENT - Security scanning integration with safety package
TODO: ENHANCE - Add more sophisticated activity analysis
TODO: IMPLEMENT - Community health metrics (contributors, issues response time)
"""

import asyncio
import math
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from .component_discovery import (
    CandidateRepo, HealthReport, MaintenanceStatus, 
    LicenseCompatibility, COMPATIBLE_LICENSES, RESTRICTIVE_LICENSES
)


class ComponentHealthAnalyzer:
    """
    Analyzes repository health using quantifiable metrics.
    
    Implements the transparent scoring algorithm with configurable weights
    and clear breakdown of how scores are calculated.
    """
    
    def __init__(self, compatible_licenses: Optional[List[str]] = None):
        """
        Initialize health analyzer with configurable license compatibility.
        
        Args:
            compatible_licenses: List of acceptable license keys. 
                                If None, uses default permissive licenses.
        """
        self.compatible_licenses = set(compatible_licenses or COMPATIBLE_LICENSES)
        
        # Scoring weights (must sum to 100)
        self.weights = {
            "activity": 40,
            "popularity": 30, 
            "license": 20,
            "security": 10
        }
    
    async def analyze_repository_health(self, repo: CandidateRepo) -> HealthReport:
        """
        Perform comprehensive health analysis of a repository.
        
        Args:
            repo: Repository candidate to analyze
            
        Returns:
            HealthReport with detailed scoring and recommendations
        """
        # Calculate individual score components
        activity_score = self._calculate_activity_score(repo)
        popularity_score = self._calculate_popularity_score(repo)
        license_score = self._calculate_license_score(repo)
        security_score = await self._calculate_security_score(repo)
        
        # Calculate overall health score
        health_score = min(100, int(
            activity_score + popularity_score + license_score + security_score
        ))
        
        # Determine maintenance status
        maintenance_status = self._determine_maintenance_status(repo)
        
        # Determine license compatibility
        license_compatibility = self._determine_license_compatibility(repo)
        
        # Calculate days since last commit
        days_since_commit = (datetime.now() - repo.pushed_at.replace(tzinfo=None)).days
        
        # Generate risk factors and positive indicators
        risk_factors = self._identify_risk_factors(repo, health_score)
        positive_indicators = self._identify_positive_indicators(repo, health_score)
        
        return HealthReport(
            repo_name=repo.full_name,
            health_score=health_score,
            activity_score=activity_score,
            popularity_score=popularity_score,
            license_score=license_score,
            security_score=security_score,
            maintenance_status=maintenance_status,
            license_compatibility=license_compatibility,
            days_since_last_commit=days_since_commit,
            vulnerability_count=0,  # TODO: IMPLEMENT - Real security scanning
            vulnerability_details=[],
            risk_factors=risk_factors,
            positive_indicators=positive_indicators
        )
    
    def _calculate_activity_score(self, repo: CandidateRepo) -> float:
        """
        Calculate activity score (0-40 points) based on recent commits.
        
        Scoring logic:
        - Recent commits (< 7 days): 40 points
        - Weekly commits (< 30 days): 35-39 points  
        - Monthly commits (< 90 days): 25-34 points
        - Quarterly commits (< 180 days): 15-24 points
        - Older commits: 0-14 points
        """
        days_since_commit = (datetime.now() - repo.pushed_at.replace(tzinfo=None)).days
        
        if days_since_commit <= 7:
            return 40.0
        elif days_since_commit <= 30:
            # Linear decay from 40 to 35 over 23 days
            return 40.0 - ((days_since_commit - 7) * 5 / 23)
        elif days_since_commit <= 90:
            # Linear decay from 35 to 25 over 60 days  
            return 35.0 - ((days_since_commit - 30) * 10 / 60)
        elif days_since_commit <= 180:
            # Linear decay from 25 to 15 over 90 days
            return 25.0 - ((days_since_commit - 90) * 10 / 90)
        elif days_since_commit <= 365:
            # Linear decay from 15 to 5 over 185 days
            return 15.0 - ((days_since_commit - 180) * 10 / 185)
        else:
            # Very old repositories get minimal points
            return max(0.0, 5.0 - (days_since_commit - 365) * 0.01)
    
    def _calculate_popularity_score(self, repo: CandidateRepo) -> float:
        """
        Calculate popularity score (0-30 points) based on community metrics.
        
        Scoring logic:
        - Stars: Logarithmic scale (more stars = diminishing returns)
        - Forks: Indicates active usage and contribution
        - Watchers: Shows ongoing interest
        - Issues: Balanced view (activity vs. problems)
        """
        # Stars score (0-20 points) - logarithmic scale
        if repo.stars <= 0:
            stars_score = 0
        else:
            # Log scale: 1 star = 2 points, 100 stars = 8 points, 10k stars = 16 points
            stars_score = min(20.0, math.log10(repo.stars) * 4)
        
        # Forks score (0-5 points) - indicates active usage
        if repo.forks <= 0:
            forks_score = 0
        else:
            forks_score = min(5.0, math.log10(repo.forks) * 2.5)
        
        # Watchers score (0-3 points) - ongoing interest
        if repo.watchers <= 0:
            watchers_score = 0
        else:
            watchers_score = min(3.0, math.log10(repo.watchers) * 1.5)
        
        # Issues penalty/bonus (0-2 points)
        # Moderate issues are good (shows activity), too many are bad
        if repo.open_issues == 0:
            issues_score = 1.0  # No issues might mean inactive
        elif repo.open_issues <= 20:
            issues_score = 2.0  # Healthy issue activity
        elif repo.open_issues <= 50:
            issues_score = 1.5  # Moderate issues
        elif repo.open_issues <= 100:
            issues_score = 1.0  # Many issues
        else:
            issues_score = 0.0  # Too many issues
        
        return stars_score + forks_score + watchers_score + issues_score
    
    def _calculate_license_score(self, repo: CandidateRepo) -> float:
        """
        Calculate license score (0-20 points) based on compatibility.
        
        Scoring logic:
        - Compatible licenses (MIT, Apache, BSD): 20 points
        - Restrictive licenses (GPL, AGPL): 10 points  
        - Unknown/No license: 0 points
        """
        if not repo.license_info:
            return 0.0
        
        license_key = repo.license_info.get("key", "").lower()
        
        if license_key in self.compatible_licenses:
            return 20.0
        elif license_key in RESTRICTIVE_LICENSES:
            return 10.0  # Usable but with restrictions
        else:
            return 5.0   # Unknown license, proceed with caution
    
    async def _calculate_security_score(self, repo: CandidateRepo) -> float:
        """
        Calculate security score (0-10 points) based on vulnerability analysis.
        
        TODO: IMPLEMENT - Real security scanning with safety package
        For now, returns mock scores based on repository characteristics.
        """
        # TODO: REPLACE_MOCK - Implement real security scanning
        await asyncio.sleep(0.01)  # Simulate security scan delay
        
        # Mock security scoring based on repository age and activity
        base_score = 10.0
        
        # Older repositories might have more vulnerabilities
        repo_age_days = (datetime.now() - repo.created_at.replace(tzinfo=None)).days
        if repo_age_days > 1000:  # > 3 years
            base_score -= 2.0
        
        # Inactive repositories are more likely to have unpatched issues
        days_since_commit = (datetime.now() - repo.pushed_at.replace(tzinfo=None)).days
        if days_since_commit > 180:  # > 6 months
            base_score -= 3.0
        elif days_since_commit > 90:  # > 3 months
            base_score -= 1.0
        
        # Very popular repositories are more likely to be secure
        if repo.stars > 10000:
            base_score += 1.0

        return max(0.0, min(10.0, base_score))
    
    def _determine_maintenance_status(self, repo: CandidateRepo) -> MaintenanceStatus:
        """Determine maintenance status based on recent activity"""
        days_since_commit = (datetime.now() - repo.pushed_at.replace(tzinfo=None)).days
        
        if days_since_commit <= 30:
            return MaintenanceStatus.ACTIVE
        elif days_since_commit <= 180:
            return MaintenanceStatus.INFREQUENT
        else:
            return MaintenanceStatus.DORMANT
    
    def _determine_license_compatibility(self, repo: CandidateRepo) -> LicenseCompatibility:
        """Determine license compatibility level"""
        if not repo.license_info:
            return LicenseCompatibility.UNKNOWN
        
        license_key = repo.license_info.get("key", "").lower()
        
        if license_key in self.compatible_licenses:
            return LicenseCompatibility.COMPATIBLE
        elif license_key in RESTRICTIVE_LICENSES:
            return LicenseCompatibility.RESTRICTIVE
        else:
            return LicenseCompatibility.UNKNOWN
    
    def _identify_risk_factors(self, repo: CandidateRepo, health_score: int) -> List[str]:
        """Identify potential risk factors for the repository"""
        risks = []
        
        days_since_commit = (datetime.now() - repo.pushed_at.replace(tzinfo=None)).days
        
        if days_since_commit > 180:
            risks.append("No recent commits (>6 months)")
        
        if repo.open_issues > 50:
            risks.append(f"High number of open issues ({repo.open_issues})")
        
        if repo.stars < 100:
            risks.append("Limited community adoption")
        
        if not repo.license_info:
            risks.append("No license specified")
        
        if repo.archived:
            risks.append("Repository is archived")
        
        if health_score < 60:
            risks.append("Overall health score below recommended threshold")
        
        return risks
    
    def _identify_positive_indicators(self, repo: CandidateRepo, health_score: int) -> List[str]:
        """Identify positive indicators for the repository"""
        positives = []
        
        days_since_commit = (datetime.now() - repo.pushed_at.replace(tzinfo=None)).days
        
        if days_since_commit <= 7:
            positives.append("Very recent activity (within 1 week)")
        elif days_since_commit <= 30:
            positives.append("Recent activity (within 1 month)")
        
        if repo.stars > 1000:
            positives.append(f"Strong community support ({repo.stars:,} stars)")
        
        if repo.forks > 100:
            positives.append(f"Active fork community ({repo.forks} forks)")
        
        if repo.license_info and repo.license_info.get("key", "").lower() in self.compatible_licenses:
            positives.append("Permissive license (easy integration)")
        
        if health_score >= 80:
            positives.append("Excellent overall health score")
        elif health_score >= 70:
            positives.append("Good overall health score")
        
        if len(repo.topics) > 3:
            positives.append("Well-documented with relevant topics")
        
        return positives
