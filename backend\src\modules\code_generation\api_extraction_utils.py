"""
API Extraction Utilities for ComponentAPIAnalyzer.

Pattern-based code analysis utilities for extracting API information from
different programming languages. This serves as the foundation before
Tree-Sitter integration in future versions.
"""

import re
from typing import Dict, List, Any


class PythonAPIExtractor:
    """Utilities for extracting API information from Python code"""
    
    @staticmethod
    def extract_classes(content: str) -> List[Dict[str, Any]]:
        """Extract class definitions from Python code"""
        classes = []
        
        # Pattern to match class definitions
        class_pattern = r'class\s+(\w+)(?:\([^)]*\))?:'
        matches = re.finditer(class_pattern, content, re.MULTILINE)
        
        for match in matches:
            class_name = match.group(1)
            
            # Extract methods from this class
            class_start = match.start()
            # Find the next class or end of file
            next_class = re.search(r'\nclass\s+\w+', content[class_start + 1:])
            class_end = class_start + next_class.start() + 1 if next_class else len(content)
            class_content = content[class_start:class_end]
            
            methods = PythonAPIExtractor.extract_methods_from_class(class_content)
            
            classes.append({
                "name": class_name,
                "methods": methods,
                "line": content[:class_start].count('\n') + 1
            })
        
        return classes
    
    @staticmethod
    def extract_methods_from_class(class_content: str) -> List[Dict[str, Any]]:
        """Extract method definitions from a class"""
        methods = []
        
        # Pattern to match method definitions
        method_pattern = r'def\s+(\w+)\s*\([^)]*\):'
        matches = re.finditer(method_pattern, class_content, re.MULTILINE)
        
        for match in matches:
            method_name = match.group(1)
            # Skip private methods (starting with _)
            if not method_name.startswith('_'):
                methods.append({
                    "name": method_name,
                    "signature": match.group(0),
                    "visibility": "public"
                })
        
        return methods
    
    @staticmethod
    def extract_functions(content: str) -> List[Dict[str, Any]]:
        """Extract standalone function definitions from Python code"""
        functions = []
        
        # Pattern to match function definitions (not inside classes)
        lines = content.split('\n')
        in_class = False
        indent_level = 0
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            
            # Track if we're inside a class
            if stripped.startswith('class '):
                in_class = True
                indent_level = len(line) - len(line.lstrip())
            elif in_class and line and len(line) - len(line.lstrip()) <= indent_level:
                in_class = False
            
            # Look for function definitions outside classes
            if not in_class and stripped.startswith('def '):
                func_match = re.match(r'def\s+(\w+)\s*\([^)]*\):', stripped)
                if func_match:
                    func_name = func_match.group(1)
                    # Skip private functions
                    if not func_name.startswith('_'):
                        functions.append({
                            "name": func_name,
                            "signature": func_match.group(0),
                            "line": i + 1
                        })
        
        return functions
    
    @staticmethod
    def extract_imports(content: str) -> List[Dict[str, str]]:
        """Extract import statements from Python code"""
        imports = []
        
        # Pattern for import statements
        import_patterns = [
            r'import\s+([\w.]+)',
            r'from\s+([\w.]+)\s+import\s+([\w,\s*]+)'
        ]
        
        for pattern in import_patterns:
            matches = re.finditer(pattern, content, re.MULTILINE)
            for match in matches:
                if 'from' in match.group(0):
                    imports.append({
                        "type": "from_import",
                        "module": match.group(1),
                        "items": match.group(2)
                    })
                else:
                    imports.append({
                        "type": "import",
                        "module": match.group(1)
                    })
        
        return imports
    
    @staticmethod
    def extract_config(content: str) -> Dict[str, Any]:
        """Extract configuration patterns from Python code"""
        config = {}
        
        # Look for common configuration patterns
        patterns = {
            "app_creation": r'app\s*=\s*FastAPI\([^)]*\)',
            "database_url": r'DATABASE_URL\s*=\s*["\']([^"\']+)["\']',
            "secret_key": r'SECRET_KEY\s*=\s*["\']([^"\']+)["\']',
            "debug_mode": r'DEBUG\s*=\s*(True|False)'
        }
        
        for key, pattern in patterns.items():
            matches = re.findall(pattern, content)
            if matches:
                config[key] = matches
        
        return config
    
    @staticmethod
    def identify_entry_points(source_files: Dict[str, str]) -> List[str]:
        """Identify entry points in Python code"""
        entry_points = []
        
        for file_path, content in source_files.items():
            # Look for main execution patterns
            if 'if __name__ == "__main__"' in content:
                entry_points.append(file_path)
            
            # Look for FastAPI app creation
            if re.search(r'app\s*=\s*FastAPI', content):
                entry_points.append(f"{file_path}:app")
        
        return entry_points
    
    @staticmethod
    def identify_patterns(source_files: Dict[str, str]) -> List[str]:
        """Identify architectural patterns in Python code"""
        patterns = []
        
        all_content = ' '.join(source_files.values())
        
        # Pattern detection
        if 'FastAPI' in all_content:
            patterns.append('fastapi')
        if 'Flask' in all_content:
            patterns.append('flask')
        if 'Django' in all_content:
            patterns.append('django')
        if 'SQLAlchemy' in all_content:
            patterns.append('sqlalchemy')
        if 'Pydantic' in all_content:
            patterns.append('pydantic')
        if 'jwt' in all_content.lower():
            patterns.append('jwt_auth')
        
        return patterns


class JavaScriptAPIExtractor:
    """Utilities for extracting API information from JavaScript/TypeScript code"""
    
    @staticmethod
    def extract_exports(content: str) -> List[Dict[str, str]]:
        """Extract export statements from JavaScript code"""
        exports = []
        
        # Pattern for various export types
        export_patterns = [
            r'module\.exports\s*=\s*(\w+)',
            r'exports\.(\w+)\s*=',
            r'export\s+(?:default\s+)?(?:function\s+)?(\w+)',
            r'export\s*{\s*([^}]+)\s*}'
        ]
        
        for pattern in export_patterns:
            matches = re.finditer(pattern, content)
            for match in matches:
                exports.append({
                    "type": "export",
                    "name": match.group(1),
                    "statement": match.group(0)
                })
        
        return exports
    
    @staticmethod
    def extract_functions(content: str) -> List[Dict[str, Any]]:
        """Extract function definitions from JavaScript code"""
        functions = []
        
        # Pattern for function definitions
        function_patterns = [
            r'function\s+(\w+)\s*\([^)]*\)',
            r'const\s+(\w+)\s*=\s*\([^)]*\)\s*=>',
            r'(\w+)\s*:\s*function\s*\([^)]*\)',
            r'app\.(\w+)\s*\([^)]*\)'  # Express route handlers
        ]
        
        for pattern in function_patterns:
            matches = re.finditer(pattern, content)
            for match in matches:
                func_name = match.group(1)
                functions.append({
                    "name": func_name,
                    "signature": match.group(0),
                    "type": "function"
                })
        
        return functions
    
    @staticmethod
    def extract_imports(content: str) -> List[Dict[str, str]]:
        """Extract import/require statements from JavaScript code"""
        imports = []
        
        # Pattern for import statements
        import_patterns = [
            r'const\s+(\w+)\s*=\s*require\(["\']([^"\']+)["\']\)',
            r'import\s+(\w+)\s+from\s+["\']([^"\']+)["\']',
            r'import\s*{\s*([^}]+)\s*}\s*from\s+["\']([^"\']+)["\']'
        ]
        
        for pattern in import_patterns:
            matches = re.finditer(pattern, content)
            for match in matches:
                if 'require' in match.group(0):
                    imports.append({
                        "type": "require",
                        "name": match.group(1),
                        "module": match.group(2)
                    })
                else:
                    imports.append({
                        "type": "import",
                        "name": match.group(1),
                        "module": match.group(2) if len(match.groups()) > 1 else ""
                    })
        
        return imports
    
    @staticmethod
    def identify_entry_points(source_files: Dict[str, str]) -> List[str]:
        """Identify entry points in JavaScript code"""
        entry_points = []
        
        for file_path, content in source_files.items():
            # Look for Express app creation
            if re.search(r'app\s*=\s*express\(\)', content):
                entry_points.append(f"{file_path}:app")
            
            # Look for server listening
            if 'app.listen' in content:
                entry_points.append(file_path)
        
        return entry_points
    
    @staticmethod
    def identify_patterns(source_files: Dict[str, str]) -> List[str]:
        """Identify architectural patterns in JavaScript code"""
        patterns = []
        
        all_content = ' '.join(source_files.values())
        
        # Pattern detection
        if 'express' in all_content:
            patterns.append('express')
        if 'react' in all_content.lower():
            patterns.append('react')
        if 'vue' in all_content.lower():
            patterns.append('vue')
        if 'socket.io' in all_content:
            patterns.append('websocket')
        
        return patterns
