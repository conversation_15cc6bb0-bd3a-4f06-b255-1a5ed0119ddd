"""
AdapterGenerator - Core adapter generation engine for component integration.

Produces glue code between selected components using intelligent context building
and specialized LLM routing. This is the core engine that creates the integration
code that connects different components together.

Key Features:
- Component-aware adapter generation using ComponentAPI analysis
- Intelligent context building via ContextWeaver
- Optimized LLM routing via SpecializedLLMClient
- Multiple adapter patterns (REST, Message Queue, Database, etc.)
- Quality-first generation with validation and testing
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from enum import Enum

from ...state.components import (
    ComponentAPI, AdapterCode, GenerationContext, CodeGenerationType,
    IntegrationComplexity, GitHubRepo
)
from ...state.project_state import ProjectState
from .context_weaver import ContextWeaver
from .specialized_llm_client import SpecializedLLMClient

logger = logging.getLogger(__name__)


class AdapterPattern(Enum):
    """Types of adapter patterns that can be generated"""
    REST_API = "rest_api"
    MESSAGE_QUEUE = "message_queue"
    DATABASE = "database"
    AUTHENTICATION = "authentication"
    CONFIGURATION = "configuration"
    EVENT_DRIVEN = "event_driven"
    GENERIC = "generic"


class AdapterGenerator:
    """
    Core adapter generation engine that creates glue code between components.
    
    Uses ComponentAPI analysis, intelligent context building, and specialized
    LLM routing to generate high-quality adapter code for component integration.
    """
    
    def __init__(
        self,
        context_weaver: Optional[ContextWeaver] = None,
        llm_client: Optional[SpecializedLLMClient] = None
    ):
        """Initialize the AdapterGenerator with required dependencies"""
        self.context_weaver = context_weaver or ContextWeaver()
        self.llm_client = llm_client or SpecializedLLMClient()
        
        # Adapter pattern templates and configurations
        self.adapter_patterns = self._initialize_adapter_patterns()
        
        # Generation statistics
        self.generation_stats = {
            "total_adapters_generated": 0,
            "successful_generations": 0,
            "pattern_usage": {},
            "complexity_distribution": {},
            "average_generation_time": 0.0
        }
    
    def _initialize_adapter_patterns(self) -> Dict[str, Any]:
        """Initialize adapter pattern configurations"""
        return {
            AdapterPattern.REST_API.value: {
                "description": "REST API integration adapter",
                "complexity": IntegrationComplexity.MEDIUM,
                "required_capabilities": ["http_client", "json_handling", "error_handling"],
                "common_patterns": ["request_response", "authentication", "rate_limiting"],
                "template_hints": [
                    "Use async/await for HTTP requests",
                    "Implement proper error handling and retries",
                    "Add request/response validation",
                    "Include authentication headers"
                ]
            },
            AdapterPattern.MESSAGE_QUEUE.value: {
                "description": "Message queue integration adapter",
                "complexity": IntegrationComplexity.HIGH,
                "required_capabilities": ["message_handling", "serialization", "connection_management"],
                "common_patterns": ["publish_subscribe", "request_reply", "message_routing"],
                "template_hints": [
                    "Implement connection pooling",
                    "Add message serialization/deserialization",
                    "Handle connection failures gracefully",
                    "Include dead letter queue handling"
                ]
            },
            AdapterPattern.DATABASE.value: {
                "description": "Database integration adapter",
                "complexity": IntegrationComplexity.MEDIUM,
                "required_capabilities": ["database_connection", "query_building", "transaction_management"],
                "common_patterns": ["repository", "unit_of_work", "connection_pooling"],
                "template_hints": [
                    "Use connection pooling for performance",
                    "Implement proper transaction handling",
                    "Add query parameterization for security",
                    "Include connection retry logic"
                ]
            },
            AdapterPattern.AUTHENTICATION.value: {
                "description": "Authentication integration adapter",
                "complexity": IntegrationComplexity.HIGH,
                "required_capabilities": ["token_handling", "session_management", "security"],
                "common_patterns": ["jwt", "oauth", "session_based", "api_key"],
                "template_hints": [
                    "Implement secure token storage",
                    "Add token refresh logic",
                    "Include proper session management",
                    "Validate all authentication data"
                ]
            },
            AdapterPattern.CONFIGURATION.value: {
                "description": "Configuration management adapter",
                "complexity": IntegrationComplexity.LOW,
                "required_capabilities": ["config_parsing", "validation", "environment_handling"],
                "common_patterns": ["environment_variables", "config_files", "remote_config"],
                "template_hints": [
                    "Support multiple configuration sources",
                    "Add configuration validation",
                    "Implement environment-specific configs",
                    "Include default value handling"
                ]
            },
            AdapterPattern.EVENT_DRIVEN.value: {
                "description": "Event-driven integration adapter",
                "complexity": IntegrationComplexity.HIGH,
                "required_capabilities": ["event_handling", "async_processing", "state_management"],
                "common_patterns": ["event_sourcing", "cqrs", "saga", "observer"],
                "template_hints": [
                    "Implement event serialization",
                    "Add event ordering guarantees",
                    "Handle event replay scenarios",
                    "Include event versioning support"
                ]
            },
            AdapterPattern.GENERIC.value: {
                "description": "Generic integration adapter",
                "complexity": IntegrationComplexity.MEDIUM,
                "required_capabilities": ["data_transformation", "error_handling", "logging"],
                "common_patterns": ["adapter", "facade", "bridge"],
                "template_hints": [
                    "Focus on clean interfaces",
                    "Add comprehensive error handling",
                    "Implement proper logging",
                    "Include data validation"
                ]
            }
        }
    
    async def generate_adapter(
        self,
        goal: str,
        source_component: str,
        target_component: str,
        project_state: ProjectState,
        adapter_pattern: Optional[AdapterPattern] = None,
        quality_level: str = "production"
    ) -> AdapterCode:
        """
        Generate adapter code between two components.
        
        Args:
            goal: High-level description of what the adapter should accomplish
            source_component: Name of the source component
            target_component: Name of the target component
            project_state: Current project state with component information
            adapter_pattern: Specific adapter pattern to use (auto-detected if None)
            quality_level: Quality level for generation (prototype, production, enterprise)
            
        Returns:
            AdapterCode object with generated code and metadata
        """
        start_time = datetime.now()
        logger.info(f"Generating adapter: {source_component} -> {target_component}")
        
        try:
            # Determine adapter pattern if not specified
            if adapter_pattern is None:
                adapter_pattern = self._determine_adapter_pattern(goal, project_state)
            
            # Build generation context
            context = self.context_weaver.build_generation_context(
                goal=goal,
                generation_type=CodeGenerationType.ADAPTER,
                project_state=project_state,
                source_component=source_component,
                target_component=target_component
            )
            
            # Enhance context with adapter-specific information
            self._enhance_context_for_adapter(context, adapter_pattern)
            
            # Build optimized prompt
            prompt = self.context_weaver.build_llm_prompt(context)
            
            # Generate adapter code using specialized LLM
            generation_result = await self.llm_client.generate_code(
                context=context,
                prompt=prompt,
                quality_level=quality_level
            )
            
            # Create AdapterCode object
            adapter_code = self._create_adapter_code(
                generation_result=generation_result,
                context=context,
                adapter_pattern=adapter_pattern,
                source_component=source_component,
                target_component=target_component,
                generation_time=(datetime.now() - start_time).total_seconds()
            )
            
            # Track generation statistics
            self._track_generation_stats(adapter_pattern, adapter_code, start_time)
            
            logger.info(f"Adapter generation complete: {adapter_code.file_name}")
            return adapter_code
            
        except Exception as e:
            logger.error(f"Error generating adapter: {e}")
            # Return minimal adapter code on error
            return self._create_fallback_adapter(
                source_component, target_component, str(e)
            )
    
    async def generate_multiple_adapters(
        self,
        adapter_requests: List[Dict[str, Any]],
        project_state: ProjectState,
        quality_level: str = "production"
    ) -> List[AdapterCode]:
        """
        Generate multiple adapters in batch for efficiency.
        
        Args:
            adapter_requests: List of adapter generation requests
            project_state: Current project state
            quality_level: Quality level for all generations
            
        Returns:
            List of generated AdapterCode objects
        """
        logger.info(f"Generating {len(adapter_requests)} adapters in batch")
        
        adapters = []
        for request in adapter_requests:
            try:
                adapter = await self.generate_adapter(
                    goal=request["goal"],
                    source_component=request["source_component"],
                    target_component=request["target_component"],
                    project_state=project_state,
                    adapter_pattern=request.get("adapter_pattern"),
                    quality_level=quality_level
                )
                adapters.append(adapter)
                
                # Small delay to avoid overwhelming the LLM API
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error in batch adapter generation: {e}")
                # Continue with other adapters even if one fails
                continue
        
        logger.info(f"Batch generation complete: {len(adapters)} adapters generated")
        return adapters
    
    def _determine_adapter_pattern(self, goal: str, project_state: ProjectState) -> AdapterPattern:
        """Determine the most appropriate adapter pattern based on goal and context"""
        goal_lower = goal.lower()
        
        # Pattern detection based on keywords
        if any(keyword in goal_lower for keyword in ["api", "rest", "http", "endpoint"]):
            return AdapterPattern.REST_API
        elif any(keyword in goal_lower for keyword in ["queue", "message", "event", "publish", "subscribe"]):
            return AdapterPattern.MESSAGE_QUEUE
        elif any(keyword in goal_lower for keyword in ["database", "db", "sql", "query", "storage"]):
            return AdapterPattern.DATABASE
        elif any(keyword in goal_lower for keyword in ["auth", "login", "token", "jwt", "oauth"]):
            return AdapterPattern.AUTHENTICATION
        elif any(keyword in goal_lower for keyword in ["config", "settings", "environment", "env"]):
            return AdapterPattern.CONFIGURATION
        elif any(keyword in goal_lower for keyword in ["event", "trigger", "notify", "callback"]):
            return AdapterPattern.EVENT_DRIVEN
        else:
            # Check project patterns for additional context
            if "rest_api" in project_state.target_patterns:
                return AdapterPattern.REST_API
            elif "message_queue" in project_state.target_patterns:
                return AdapterPattern.MESSAGE_QUEUE
            else:
                return AdapterPattern.GENERIC
    
    def _enhance_context_for_adapter(self, context: GenerationContext, adapter_pattern: AdapterPattern):
        """Enhance generation context with adapter-specific information"""
        pattern_config = self.adapter_patterns[adapter_pattern.value]
        
        # Add adapter pattern information to context
        context.adapter_pattern = adapter_pattern.value
        context.adapter_complexity = pattern_config["complexity"]
        context.required_capabilities = pattern_config["required_capabilities"]
        context.template_hints = pattern_config["template_hints"]
        
        # Adjust quality requirements based on pattern complexity
        if pattern_config["complexity"] == IntegrationComplexity.HIGH:
            context.quality_level = "enterprise"
        elif pattern_config["complexity"] == IntegrationComplexity.VERY_HIGH:
            context.quality_level = "enterprise"
            context.include_tests = True
            context.include_documentation = True

    def _create_adapter_code(
        self,
        generation_result: Dict[str, Any],
        context: GenerationContext,
        adapter_pattern: AdapterPattern,
        source_component: str,
        target_component: str,
        generation_time: float
    ) -> AdapterCode:
        """Create AdapterCode object from generation result"""

        # Generate appropriate file name
        file_name = self._generate_adapter_filename(
            source_component, target_component, adapter_pattern
        )

        # Extract generated code
        generated_code = generation_result.get("generated_code", "")

        # Create AdapterCode object
        adapter_code = AdapterCode(
            source_component=source_component,
            target_component=target_component,
            pattern_type=adapter_pattern.value,
            integration_complexity=context.adapter_complexity if hasattr(context, 'adapter_complexity') else IntegrationComplexity.MEDIUM,
            adapter_code=generated_code,
            file_path=file_name,
            language=context.project_language,
            dependencies=self._extract_dependencies(generated_code, context),
            complexity_score=self._calculate_complexity_score(context),
            generation_model=generation_result.get("model_used", "unknown"),
            generation_tokens=generation_result.get("token_usage", 0),
            generation_time_seconds=generation_time
        )

        # Calculate metrics
        adapter_code.calculate_metrics()

        return adapter_code

    def _generate_adapter_filename(
        self,
        source_component: str,
        target_component: str,
        adapter_pattern: AdapterPattern
    ) -> str:
        """Generate appropriate filename for the adapter"""
        # Clean component names
        source_clean = source_component.replace("-", "_").replace(" ", "_").lower()
        target_clean = target_component.replace("-", "_").replace(" ", "_").lower()

        # Generate filename based on pattern
        if adapter_pattern == AdapterPattern.REST_API:
            return f"{source_clean}_to_{target_clean}_api_adapter.py"
        elif adapter_pattern == AdapterPattern.MESSAGE_QUEUE:
            return f"{source_clean}_to_{target_clean}_queue_adapter.py"
        elif adapter_pattern == AdapterPattern.DATABASE:
            return f"{source_clean}_to_{target_clean}_db_adapter.py"
        elif adapter_pattern == AdapterPattern.AUTHENTICATION:
            return f"{source_clean}_to_{target_clean}_auth_adapter.py"
        elif adapter_pattern == AdapterPattern.CONFIGURATION:
            return f"{source_clean}_to_{target_clean}_config_adapter.py"
        elif adapter_pattern == AdapterPattern.EVENT_DRIVEN:
            return f"{source_clean}_to_{target_clean}_event_adapter.py"
        else:
            return f"{source_clean}_to_{target_clean}_adapter.py"

    def _calculate_quality_score(self, generation_result: Dict[str, Any]) -> float:
        """Calculate quality score based on generation result"""
        base_score = 0.7  # Base score for successful generation

        # Adjust based on model used
        model_used = generation_result.get("model_used", "").lower()
        if "claude" in model_used or "gpt-4" in model_used:
            base_score += 0.2  # Premium models get higher base score
        elif "deepseek" in model_used:
            base_score += 0.1  # Fast models get moderate boost

        # Adjust based on code length (more comprehensive = higher quality)
        code_length = len(generation_result.get("generated_code", ""))
        if code_length > 1000:
            base_score += 0.1
        elif code_length < 200:
            base_score -= 0.1

        # Ensure score is within bounds
        return max(0.0, min(1.0, base_score))

    def _calculate_complexity_score(self, context: GenerationContext) -> float:
        """Calculate complexity score based on generation context"""
        complexity_score = 0.0

        # Base complexity from adapter pattern
        if hasattr(context, 'adapter_complexity'):
            complexity_mapping = {
                IntegrationComplexity.LOW: 0.2,
                IntegrationComplexity.MEDIUM: 0.4,
                IntegrationComplexity.HIGH: 0.6,
                IntegrationComplexity.VERY_HIGH: 0.8
            }
            complexity_score += complexity_mapping.get(context.adapter_complexity, 0.4)

        # Add component API complexity
        if context.source_component_api:
            complexity_score += context.source_component_api.get_complexity_score() * 0.3
        if context.target_component_api:
            complexity_score += context.target_component_api.get_complexity_score() * 0.3

        # Ensure score is within bounds
        return max(0.0, min(1.0, complexity_score))

    def _extract_dependencies(self, generated_code: str, context: GenerationContext) -> List[str]:
        """Extract dependencies from generated code"""
        dependencies = []

        # Extract Python imports
        if context.project_language == "python":
            import_lines = [line.strip() for line in generated_code.split('\n')
                          if line.strip().startswith(('import ', 'from '))]

            for line in import_lines:
                if line.startswith('import '):
                    module = line.replace('import ', '').split('.')[0].split(' as ')[0]
                    if module not in ['os', 'sys', 'json', 'datetime', 'typing']:  # Skip stdlib
                        dependencies.append(module)
                elif line.startswith('from '):
                    module = line.split(' ')[1].split('.')[0]
                    if module not in ['os', 'sys', 'json', 'datetime', 'typing']:  # Skip stdlib
                        dependencies.append(module)

        # Extract JavaScript requires/imports
        elif context.project_language in ["javascript", "typescript"]:
            lines = generated_code.split('\n')
            for line in lines:
                line = line.strip()
                if 'require(' in line or 'import ' in line:
                    # Extract module name from require/import statements
                    if 'require(' in line:
                        start = line.find("'") or line.find('"')
                        if start != -1:
                            end = line.find("'", start + 1) or line.find('"', start + 1)
                            if end != -1:
                                module = line[start + 1:end]
                                if not module.startswith('.'):  # Skip relative imports
                                    dependencies.append(module)
                    elif 'import ' in line and ' from ' in line:
                        # Handle "import React from 'react'"
                        parts = line.split(' from ')
                        if len(parts) > 1:
                            module_part = parts[1].strip().rstrip(';')
                            # Find quote characters
                            quote_chars = ["'", '"']
                            for quote in quote_chars:
                                start = module_part.find(quote)
                                if start != -1:
                                    end = module_part.find(quote, start + 1)
                                    if end != -1:
                                        module = module_part[start + 1:end]
                                        if not module.startswith('.'):
                                            dependencies.append(module)
                                        break

        return list(set(dependencies))  # Remove duplicates

    def _create_fallback_adapter(
        self,
        source_component: str,
        target_component: str,
        error_message: str
    ) -> AdapterCode:
        """Create fallback adapter code when generation fails"""
        file_name = f"{source_component}_to_{target_component}_adapter.py"

        fallback_code = f'''
"""
Fallback adapter between {source_component} and {target_component}.
Generated due to error in primary generation: {error_message}
"""

import logging

logger = logging.getLogger(__name__)

class {source_component.title()}To{target_component.title()}Adapter:
    """
    Fallback adapter implementation.

    This is a minimal adapter that needs to be implemented manually
    due to an error in automatic generation.
    """

    def __init__(self, source, target):
        self.source = source
        self.target = target
        logger.warning("Using fallback adapter implementation")

    def adapt(self, data):
        """
        Adapt data from source to target format.

        TODO: Implement actual adaptation logic
        """
        logger.info("Adapting data using fallback implementation")
        # Basic pass-through - needs manual implementation
        return self.target.process(data)

    def validate_connection(self):
        """Validate that both components are properly connected"""
        return self.source is not None and self.target is not None
'''

        adapter_code = AdapterCode(
            source_component=source_component,
            target_component=target_component,
            pattern_type="fallback",
            integration_complexity=IntegrationComplexity.LOW,
            adapter_code=fallback_code,
            file_path=file_name,
            language="python",
            dependencies=[],
            complexity_score=0.2,
            generation_model="fallback",
            generation_tokens=0,
            generation_time_seconds=0.0
        )

        # Add error metadata (store in documentation field for now)
        adapter_code.documentation = f"Error: {error_message}\nFallback: True"

        return adapter_code

    def _track_generation_stats(
        self,
        adapter_pattern: AdapterPattern,
        adapter_code: AdapterCode,
        start_time: datetime
    ):
        """Track generation statistics for optimization"""
        generation_time = (datetime.now() - start_time).total_seconds()

        # Update overall stats
        self.generation_stats["total_adapters_generated"] += 1
        if adapter_code.complexity_score > 0.0 and len(adapter_code.adapter_code) > 100:
            self.generation_stats["successful_generations"] += 1

        # Update pattern usage
        pattern_name = adapter_pattern.value
        if pattern_name not in self.generation_stats["pattern_usage"]:
            self.generation_stats["pattern_usage"][pattern_name] = 0
        self.generation_stats["pattern_usage"][pattern_name] += 1

        # Update complexity distribution
        complexity_range = self._get_complexity_range(adapter_code.complexity_score)
        if complexity_range not in self.generation_stats["complexity_distribution"]:
            self.generation_stats["complexity_distribution"][complexity_range] = 0
        self.generation_stats["complexity_distribution"][complexity_range] += 1

        # Update average generation time
        total_time = (self.generation_stats["average_generation_time"] *
                     (self.generation_stats["total_adapters_generated"] - 1) + generation_time)
        self.generation_stats["average_generation_time"] = (
            total_time / self.generation_stats["total_adapters_generated"]
        )

    def _get_complexity_range(self, complexity_score: float) -> str:
        """Get complexity range for statistics"""
        if complexity_score < 0.3:
            return "low"
        elif complexity_score < 0.6:
            return "medium"
        elif complexity_score < 0.8:
            return "high"
        else:
            return "very_high"

    def get_generation_statistics(self) -> Dict[str, Any]:
        """Get comprehensive generation statistics"""
        success_rate = 0.0
        if self.generation_stats["total_adapters_generated"] > 0:
            success_rate = (self.generation_stats["successful_generations"] /
                          self.generation_stats["total_adapters_generated"])

        return {
            **self.generation_stats,
            "success_rate": success_rate,
            "supported_patterns": list(self.adapter_patterns.keys())
        }

    def get_pattern_recommendations(self, goal: str, project_state: ProjectState) -> List[Dict[str, Any]]:
        """Get adapter pattern recommendations for a given goal"""
        recommendations = []

        for pattern_name, pattern_config in self.adapter_patterns.items():
            # Calculate suitability score
            suitability_score = self._calculate_pattern_suitability(
                goal, pattern_name, pattern_config, project_state
            )

            recommendations.append({
                "pattern": pattern_name,
                "suitability_score": suitability_score,
                "description": pattern_config["description"],
                "complexity": pattern_config["complexity"].value,
                "required_capabilities": pattern_config["required_capabilities"],
                "template_hints": pattern_config["template_hints"][:3]  # Top 3 hints
            })

        # Sort by suitability score
        recommendations.sort(key=lambda x: x["suitability_score"], reverse=True)
        return recommendations

    def _calculate_pattern_suitability(
        self,
        goal: str,
        pattern_name: str,
        pattern_config: Dict[str, Any],
        project_state: ProjectState
    ) -> float:
        """Calculate how suitable a pattern is for the given goal"""
        goal_lower = goal.lower()
        suitability_score = 0.0

        # Keyword matching
        pattern_keywords = {
            "rest_api": ["api", "rest", "http", "endpoint", "web"],
            "message_queue": ["queue", "message", "event", "publish", "subscribe", "async"],
            "database": ["database", "db", "sql", "storage", "persist", "query"],
            "authentication": ["auth", "login", "token", "jwt", "oauth", "security"],
            "configuration": ["config", "settings", "environment", "env", "setup"],
            "event_driven": ["event", "trigger", "notify", "callback", "reactive"],
            "generic": ["integrate", "connect", "bridge", "adapter"]
        }

        keywords = pattern_keywords.get(pattern_name, [])
        keyword_matches = sum(1 for keyword in keywords if keyword in goal_lower)
        suitability_score += (keyword_matches / max(len(keywords), 1)) * 0.6

        # Project pattern alignment
        if pattern_name in project_state.target_patterns:
            suitability_score += 0.3

        # Usage history (if available)
        if pattern_name in self.generation_stats["pattern_usage"]:
            usage_count = self.generation_stats["pattern_usage"][pattern_name]
            # Slight boost for proven patterns
            suitability_score += min(usage_count * 0.01, 0.1)

        return min(suitability_score, 1.0)
