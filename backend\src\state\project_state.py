"""
Core data models for CodeQuilter project state management.

The ProjectState class is the single source of truth for all project data,
including user intent, selected components, generated code, and verification results.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
import uuid


class ProjectStatus(Enum):
    """Current status of the project generation process"""
    INITIALIZING = "initializing"
    BRAINSTORMING = "brainstorming"
    ARCHITECTING = "architecting"
    PROCURING = "procuring"
    QUILTING = "quilting"
    VERIFYING = "verifying"
    COMPLETED = "completed"
    ERROR = "error"


@dataclass
class StateChange:
    """Represents a single change to project state for undo/redo functionality"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = field(default_factory=datetime.now)
    action: str = ""  # e.g., "component_selected", "code_generated"
    description: str = ""  # Human-readable description
    previous_state: Optional[Dict[str, Any]] = None
    new_state: Optional[Dict[str, Any]] = None


@dataclass
class ProjectState:
    """
    Central state object for a CodeQuilter project.
    
    This class represents the complete state of a project generation session,
    from initial user intent through final code generation and verification.
    All modules operate on this state object.
    """
    
    # Core Identity
    session_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    created_at: datetime = field(default_factory=datetime.now)
    last_modified: datetime = field(default_factory=datetime.now)
    status: ProjectStatus = ProjectStatus.INITIALIZING
    
    # User Intent & Planning (Phase: Brainstorming)
    project_brief: str = ""  # User's description of what they want to build
    project_name: str = ""   # Generated or user-provided project name
    target_patterns: List[str] = field(default_factory=list)  # Pattern names to use
    
    # Architecture & Component Discovery (Phase: Procurement)
    candidate_components: Dict[str, List[Dict[str, Any]]] = field(default_factory=dict)  # pattern -> repos
    selected_components: Dict[str, Dict[str, Any]] = field(default_factory=dict)  # pattern -> chosen repo
    
    # Code Generation & Assembly (Phase: Quilting)
    generated_adapters: Dict[str, Dict[str, Any]] = field(default_factory=dict)  # component_pair -> adapter
    project_structure: Dict[str, Any] = field(default_factory=dict)  # File tree structure

    # Enhanced Code Generation Data (Code Generation & Assembly Module)
    component_apis: Dict[str, Dict[str, Any]] = field(default_factory=dict)  # component -> API analysis
    adapter_codes: Dict[str, Dict[str, Any]] = field(default_factory=dict)   # adapter_id -> AdapterCode
    validation_results: Dict[str, Dict[str, Any]] = field(default_factory=dict)  # code_id -> ValidationResult
    generation_context: Optional[Dict[str, Any]] = None  # Current generation context
    
    # Quality & Verification (Phase: Verification)
    test_results: Dict[str, Dict[str, Any]] = field(default_factory=dict)  # component -> results
    security_scan: Dict[str, Any] = field(default_factory=dict)  # Security analysis results
    
    # State Management
    generation_history: List[StateChange] = field(default_factory=list)
    current_step: str = "initialization"
    error_messages: List[str] = field(default_factory=list)

    # TaskExecutionAgent Integration
    active_task_list: Optional[List[Dict[str, Any]]] = None  # Current task list being executed
    task_execution_context: Optional[Dict[str, Any]] = None  # Context for task execution
    
    def update_status(self, new_status: ProjectStatus, step: str = "") -> None:
        """Update project status and record the change"""
        old_status = self.status
        self.status = new_status
        self.last_modified = datetime.now()
        if step:
            self.current_step = step
            
        # Record state change
        change = StateChange(
            action="status_update",
            description=f"Status changed from {old_status.value} to {new_status.value}",
            previous_state={"status": old_status.value},
            new_state={"status": new_status.value}
        )
        self.generation_history.append(change)
    
    def add_error(self, error_message: str) -> None:
        """Add an error message and update status"""
        self.error_messages.append(error_message)
        self.update_status(ProjectStatus.ERROR)
    
    def clear_errors(self) -> None:
        """Clear all error messages"""
        self.error_messages.clear()
        if self.status == ProjectStatus.ERROR:
            # Revert to previous status if possible
            if self.generation_history:
                last_change = self.generation_history[-1]
                if last_change.previous_state and "status" in last_change.previous_state:
                    self.status = ProjectStatus(last_change.previous_state["status"])
    
    def select_component(self, pattern: str, component: Dict[str, Any]) -> None:
        """Select a component for a specific pattern"""
        self.selected_components[pattern] = component
        self.last_modified = datetime.now()
        
        change = StateChange(
            action="component_selected",
            description=f"Selected component for {pattern}: {component.get('name', 'unknown')}",
            new_state={"pattern": pattern, "component": component}
        )
        self.generation_history.append(change)
    
    def add_generated_code(self, component_pair: str, code_data: Dict[str, Any]) -> None:
        """Add generated adapter code"""
        self.generated_adapters[component_pair] = code_data
        self.last_modified = datetime.now()
        
        change = StateChange(
            action="code_generated",
            description=f"Generated adapter code for {component_pair}",
            new_state={"component_pair": component_pair, "code_data": code_data}
        )
        self.generation_history.append(change)
    
    def get_selected_component_names(self) -> List[str]:
        """Get list of selected component names"""
        return [comp.get("name", "unknown") for comp in self.selected_components.values()]
    
    def get_progress_percentage(self) -> int:
        """Calculate overall progress percentage"""
        status_weights = {
            ProjectStatus.INITIALIZING: 0,
            ProjectStatus.BRAINSTORMING: 15,
            ProjectStatus.ARCHITECTING: 30,
            ProjectStatus.PROCURING: 50,
            ProjectStatus.QUILTING: 75,
            ProjectStatus.VERIFYING: 90,
            ProjectStatus.COMPLETED: 100,
            ProjectStatus.ERROR: 0
        }
        return status_weights.get(self.status, 0)
    
    def can_undo(self) -> bool:
        """Check if undo operation is possible"""
        return len(self.generation_history) > 0

    def set_active_task_list(self, tasks: List[Dict[str, Any]], context: Dict[str, Any] = None) -> None:
        """Set the active task list for TaskExecutionAgent"""
        self.active_task_list = tasks
        self.task_execution_context = context or {}
        self.last_modified = datetime.now()

        change = StateChange(
            action="task_list_set",
            description=f"Set active task list with {len(tasks)} tasks",
            new_state={"task_count": len(tasks), "context": context}
        )
        self.generation_history.append(change)

    def update_task_in_list(self, task_id: str, task_data: Dict[str, Any]) -> bool:
        """Update a specific task in the active task list"""
        if not self.active_task_list:
            return False

        for i, task in enumerate(self.active_task_list):
            if task.get("id") == task_id:
                self.active_task_list[i] = task_data
                self.last_modified = datetime.now()
                return True
        return False

    def clear_active_task_list(self) -> None:
        """Clear the active task list after execution"""
        self.active_task_list = None
        self.task_execution_context = None
        self.last_modified = datetime.now()

    # ============================================================================
    # Code Generation & Assembly Module Methods
    # ============================================================================

    def add_component_api(self, component_name: str, api_data: Dict[str, Any]) -> None:
        """Add component API analysis results"""
        self.component_apis[component_name] = api_data
        self.last_modified = datetime.now()

        change = StateChange(
            action="component_api_analyzed",
            description=f"Analyzed API for component: {component_name}",
            new_state={"component": component_name, "api_data": api_data}
        )
        self.generation_history.append(change)

    def add_adapter_code(self, adapter_id: str, adapter_data: Dict[str, Any]) -> None:
        """Add generated adapter code"""
        self.adapter_codes[adapter_id] = adapter_data
        self.last_modified = datetime.now()

        change = StateChange(
            action="adapter_code_generated",
            description=f"Generated adapter code: {adapter_id}",
            new_state={"adapter_id": adapter_id, "adapter_data": adapter_data}
        )
        self.generation_history.append(change)

    def add_validation_result(self, code_id: str, validation_data: Dict[str, Any]) -> None:
        """Add code validation results"""
        self.validation_results[code_id] = validation_data
        self.last_modified = datetime.now()

        change = StateChange(
            action="code_validated",
            description=f"Validated code: {code_id}",
            new_state={"code_id": code_id, "validation_data": validation_data}
        )
        self.generation_history.append(change)

    def set_generation_context(self, context: Dict[str, Any]) -> None:
        """Set the current generation context"""
        self.generation_context = context
        self.last_modified = datetime.now()

        change = StateChange(
            action="generation_context_set",
            description="Set generation context for code generation",
            new_state={"context": context}
        )
        self.generation_history.append(change)

    def get_code_generation_progress(self) -> Dict[str, Any]:
        """Get detailed progress of code generation phase"""
        total_components = len(self.selected_components)
        analyzed_components = len(self.component_apis)
        generated_adapters = len(self.adapter_codes)
        validated_codes = len(self.validation_results)

        return {
            "total_components": total_components,
            "analyzed_components": analyzed_components,
            "generated_adapters": generated_adapters,
            "validated_codes": validated_codes,
            "analysis_progress": (analyzed_components / total_components * 100) if total_components > 0 else 0,
            "generation_progress": (generated_adapters / max(total_components, 1) * 100),
            "validation_progress": (validated_codes / max(generated_adapters, 1) * 100) if generated_adapters > 0 else 0
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        code_gen_progress = self.get_code_generation_progress()

        return {
            "session_id": self.session_id,
            "created_at": self.created_at.isoformat(),
            "last_modified": self.last_modified.isoformat(),
            "status": self.status.value,
            "project_brief": self.project_brief,
            "project_name": self.project_name,
            "target_patterns": self.target_patterns,
            "selected_components": self.selected_components,
            "current_step": self.current_step,
            "progress": self.get_progress_percentage(),
            "error_messages": self.error_messages,
            "component_count": len(self.selected_components),
            "adapter_count": len(self.generated_adapters),
            "active_task_list": self.active_task_list,
            "task_execution_context": self.task_execution_context,
            "has_active_tasks": self.active_task_list is not None,

            # Code Generation & Assembly Module data
            "component_apis_count": len(self.component_apis),
            "adapter_codes_count": len(self.adapter_codes),
            "validation_results_count": len(self.validation_results),
            "has_generation_context": self.generation_context is not None,
            "code_generation_progress": code_gen_progress
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ProjectState":
        """Create ProjectState from dictionary"""
        # TODO: REPLACE_MOCK - Implement full deserialization when needed
        state = cls()
        state.session_id = data.get("session_id", state.session_id)
        state.project_brief = data.get("project_brief", "")
        state.project_name = data.get("project_name", "")
        state.status = ProjectStatus(data.get("status", "initializing"))
        return state
