"""
Real LLM client implementation for CodeQuilter.

Supports multiple LLM providers:
- DeepSeek Reasoner via DEEPSEEK_API_KEY
- OpenRouter/Gemini via OPENROUTER_API_KEY

This replaces the mock LLM client for production use.
"""

import os
import asyncio
import httpx
from typing import Dict, Any, Optional
from dataclasses import dataclass

from .base_client import LLMClientInterface, APIResponse


@dataclass
class LLMConfig:
    """Configuration for LLM providers"""
    provider: str
    api_key: str
    base_url: str
    model: str
    max_tokens: int = 2000
    temperature: float = 0.7


class RealLLMClient(LLMClientInterface):
    """
    Real LLM client supporting multiple providers.
    
    Automatically detects available API keys and selects the best provider.
    """
    
    def __init__(self, preferred_provider: str = "auto"):
        self.configs = self._detect_available_providers()
        self.active_config = self._select_provider(preferred_provider)
        self._token_usage = 0
        
        if not self.active_config:
            raise ValueError("No LLM API keys found. Please set DEEPSEEK_API_KEY or OPENROUTER_API_KEY environment variables.")
        
        super().__init__(api_key=self.active_config.api_key, base_url=self.active_config.base_url)
        print(f"🤖 LLM Client initialized with {self.active_config.provider} ({self.active_config.model})")
    
    def _detect_available_providers(self) -> Dict[str, LLMConfig]:
        """Detect available LLM providers from environment variables"""
        configs = {}
        
        # DeepSeek Reasoner
        deepseek_key = os.getenv("DEEPSEEK_API_KEY")
        if deepseek_key:
            configs["deepseek"] = LLMConfig(
                provider="deepseek",
                api_key=deepseek_key,
                base_url="https://api.deepseek.com/v1",
                model="deepseek-reasoner",
                max_tokens=4000,
                temperature=0.7
            )
        
        # OpenRouter/Gemini
        openrouter_key = os.getenv("OPENROUTER_API_KEY")
        if openrouter_key:
            configs["openrouter"] = LLMConfig(
                provider="openrouter",
                api_key=openrouter_key,
                base_url="https://openrouter.ai/api/v1",
                model="google/gemini-2.5-pro-preview",
                max_tokens=8000,
                temperature=0.6
            )
        
        return configs
    
    def _select_provider(self, preferred: str) -> Optional[LLMConfig]:
        """Select the best available provider"""
        if preferred != "auto" and preferred in self.configs:
            return self.configs[preferred]
        
        # Auto-selection priority: OpenRouter/Gemini > DeepSeek
        if "openrouter" in self.configs:
            return self.configs["openrouter"]
        elif "deepseek" in self.configs:
            return self.configs["deepseek"]
        
        return None
    
    async def test_connection(self) -> APIResponse:
        """Test LLM API connection"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                headers = {
                    "Authorization": f"Bearer {self.active_config.api_key}",
                    "Content-Type": "application/json"
                }
                
                # Simple test prompt
                data = {
                    "model": self.active_config.model,
                    "messages": [{"role": "user", "content": "Hello! Please respond with 'Connection successful'"}],
                    "max_tokens": 50,
                    "temperature": 0.1
                }
                
                response = await client.post(
                    f"{self.active_config.base_url}/chat/completions",
                    headers=headers,
                    json=data
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return self._format_response(True, {
                        "status": "connected",
                        "provider": self.active_config.provider,
                        "model": self.active_config.model,
                        "response": result.get("choices", [{}])[0].get("message", {}).get("content", "")
                    })
                else:
                    return self._format_response(False, {
                        "error": f"HTTP {response.status_code}: {response.text}"
                    })
                    
        except Exception as e:
            return self._format_response(False, {
                "error": f"Connection failed: {str(e)}"
            })
    
    async def generate_brainstorming_response(self, prompt: str, context: Dict[str, Any]) -> APIResponse:
        """Generate intelligent brainstorming responses"""
        try:
            # Build context-aware prompt
            system_prompt = """You are CodeQuilter's intelligent brainstorming assistant. Your role is to help users define their software project requirements through progressive questioning.

Based on the conversation context, provide:
1. Intelligent follow-up questions (if needed)
2. Pattern confidence assessments
3. Technology recommendations with reasoning
4. Clear explanations for your suggestions

Be concise, practical, and focus on actionable insights."""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ]
            
            async with httpx.AsyncClient(timeout=60.0) as client:
                headers = {
                    "Authorization": f"Bearer {self.active_config.api_key}",
                    "Content-Type": "application/json"
                }
                
                data = {
                    "model": self.active_config.model,
                    "messages": messages,
                    "max_tokens": self.active_config.max_tokens,
                    "temperature": self.active_config.temperature
                }
                
                response = await client.post(
                    f"{self.active_config.base_url}/chat/completions",
                    headers=headers,
                    json=data
                )
                
                if response.status_code == 200:
                    result = response.json()
                    content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                    tokens_used = result.get("usage", {}).get("total_tokens", 0)
                    self._token_usage += tokens_used
                    
                    return self._format_response(True, {
                        "response": content,
                        "tokens_used": tokens_used,
                        "model": self.active_config.model,
                        "provider": self.active_config.provider
                    })
                else:
                    return self._format_response(False, {
                        "error": f"HTTP {response.status_code}: {response.text}"
                    })
                    
        except Exception as e:
            return self._format_response(False, {
                "error": f"Generation failed: {str(e)}"
            })
    
    async def generate_code(self, prompt: str, max_tokens: int = 1000) -> APIResponse:
        """Generate code based on a prompt (legacy compatibility)"""
        return await self.generate_brainstorming_response(prompt, {})
    
    async def analyze_code(self, code: str, analysis_type: str = "quality") -> APIResponse:
        """Analyze code for quality, security, or other metrics"""
        prompt = f"""Analyze this code for {analysis_type}:

```
{code}
```

Provide a structured analysis with scores and specific recommendations."""
        
        return await self.generate_brainstorming_response(prompt, {"analysis_type": analysis_type})
    
    async def generate_documentation(self, code: str) -> APIResponse:
        """Generate documentation for code"""
        prompt = f"""Generate comprehensive documentation for this code:

```
{code}
```

Include overview, functions, usage examples, and dependencies."""
        
        return await self.generate_brainstorming_response(prompt, {"task": "documentation"})
    
    async def suggest_improvements(self, code: str) -> APIResponse:
        """Suggest improvements for code"""
        prompt = f"""Suggest specific improvements for this code:

```
{code}
```

Focus on performance, readability, and best practices. Provide actionable suggestions."""
        
        return await self.generate_brainstorming_response(prompt, {"task": "improvements"})
    
    def get_token_usage(self) -> int:
        """Get total token usage for this session"""
        return self._token_usage
    
    def reset_token_usage(self) -> None:
        """Reset token usage counter"""
        self._token_usage = 0
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get information about the active provider"""
        if not self.active_config:
            return {"provider": "none", "status": "no_api_keys"}
        
        return {
            "provider": self.active_config.provider,
            "model": self.active_config.model,
            "base_url": self.active_config.base_url,
            "max_tokens": self.active_config.max_tokens,
            "temperature": self.active_config.temperature,
            "available_providers": list(self.configs.keys())
        }


# Factory function to create the appropriate client
def create_llm_client(use_real_api: bool = True, preferred_provider: str = "auto") -> LLMClientInterface:
    """
    Factory function to create LLM client.
    
    Args:
        use_real_api: If True, use real LLM APIs. If False, use mock client.
        preferred_provider: "auto", "deepseek", or "openrouter"
    
    Returns:
        LLMClientInterface instance
    """
    if use_real_api:
        try:
            return RealLLMClient(preferred_provider)
        except ValueError as e:
            print(f"⚠️  Real LLM client unavailable: {e}")
            print("🔄 Falling back to mock LLM client")
            from .llm_client import LLMClient
            return LLMClient()
    else:
        from .llm_client import LLMClient
        return LLMClient()


# Global instance - can be switched between real and mock
llm_client = create_llm_client(use_real_api=True)
