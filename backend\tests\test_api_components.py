"""
Tests for component discovery API endpoints.

Tests the REST API for component search, recommendation, and selection,
including integration with brainstorming results and project state.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch
from datetime import datetime

from backend.src.main import app
from backend.src.session_manager import session_manager
from backend.src.state.project_state import ProjectState, ProjectStatus
from backend.src.modules.brainstorming import BrainstormingSession
from backend.src.api.brainstorming import brainstorming_sessions


class TestComponentSearchAPI:
    """Test component search API endpoints"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)
    
    @pytest.fixture
    def sample_project_state(self):
        """Create sample project state for testing"""
        return ProjectState(
            session_id="test-session-123",
            project_brief="Test REST API project",
            status=ProjectStatus.BRAINSTORMING
        )
    
    @pytest.fixture
    def sample_brainstorming_session(self):
        """Create completed brainstorming session for testing"""
        session = BrainstormingSession(
            session_id="brainstorm-123",
            project_description="Test REST API project",
            conversation_complete=True
        )
        
        # Add sample responses
        from backend.src.modules.brainstorming import QuestionnaireResponse
        session.responses = [
            QuestionnaireResponse(
                question_id="developer_preferences",
                question_text="What programming language do you prefer?",
                answer="Python"
            ),
            QuestionnaireResponse(
                question_id="communication_style",
                question_text="How will users interact with your application?",
                answer="Web API (REST/GraphQL)"
            )
        ]
        
        # Add pattern scores
        from backend.src.modules.brainstorming import PatternConfidence
        session.pattern_scores = {
            "rest_api": PatternConfidence(
                pattern_name="rest_api",
                confidence=0.85,
                reasoning="Strong match for REST API pattern"
            )
        }
        
        return session
    
    def test_search_components_success(self, client, sample_project_state, sample_brainstorming_session):
        """Test successful component search"""
        # Setup session manager with test data
        session_manager._active_sessions[sample_project_state.session_id] = sample_project_state
        brainstorming_sessions[sample_brainstorming_session.session_id] = sample_brainstorming_session
        
        # Make request
        response = client.post(
            f"/api/projects/{sample_project_state.session_id}/components/search",
            json={
                "brainstorming_session_id": sample_brainstorming_session.session_id,
                "max_recommendations": 3,
                "use_real_apis": False
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["success"] is True
        assert data["pattern_count"] > 0
        assert "recommendations" in data
        assert "search_metadata" in data
        
        # Check that project state was updated
        updated_project = session_manager.get_session(sample_project_state.session_id)
        assert updated_project.status == ProjectStatus.PROCURING
        assert len(updated_project.candidate_components) > 0
    
    def test_search_components_project_not_found(self, client):
        """Test component search with non-existent project"""
        response = client.post(
            "/api/projects/nonexistent-session/components/search",
            json={
                "brainstorming_session_id": "brainstorm-123",
                "max_recommendations": 3,
                "use_real_apis": False
            }
        )
        
        assert response.status_code == 404
        assert "Project session not found" in response.json()["detail"]
    
    def test_search_components_brainstorming_not_found(self, client, sample_project_state):
        """Test component search with non-existent brainstorming session"""
        session_manager._active_sessions[sample_project_state.session_id] = sample_project_state
        
        response = client.post(
            f"/api/projects/{sample_project_state.session_id}/components/search",
            json={
                "brainstorming_session_id": "nonexistent-brainstorm",
                "max_recommendations": 3,
                "use_real_apis": False
            }
        )
        
        assert response.status_code == 404
        assert "Brainstorming session not found" in response.json()["detail"]
    
    def test_search_components_brainstorming_incomplete(self, client, sample_project_state):
        """Test component search with incomplete brainstorming session"""
        session_manager._active_sessions[sample_project_state.session_id] = sample_project_state

        # Create incomplete brainstorming session
        incomplete_session = BrainstormingSession(
            session_id="incomplete-brainstorm",
            conversation_complete=False
        )
        brainstorming_sessions[incomplete_session.session_id] = incomplete_session
        
        response = client.post(
            f"/api/projects/{sample_project_state.session_id}/components/search",
            json={
                "brainstorming_session_id": incomplete_session.session_id,
                "max_recommendations": 3,
                "use_real_apis": False
            }
        )
        
        assert response.status_code == 400
        assert "must be completed" in response.json()["detail"]
    
    def test_get_component_candidates_success(self, client, sample_project_state):
        """Test getting component candidates"""
        # Add candidate components to project state
        sample_project_state.candidate_components = {
            "rest_api": [
                {
                    "candidate_repo": {
                        "name": "fastapi-advanced",
                        "full_name": "awesome/fastapi-advanced",
                        "stars": 5000
                    },
                    "health_report": {
                        "health_score": 85
                    }
                }
            ]
        }
        session_manager._active_sessions[sample_project_state.session_id] = sample_project_state
        
        response = client.get(f"/api/projects/{sample_project_state.session_id}/components/candidates")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["success"] is True
        assert "candidates" in data
        assert "rest_api" in data["candidates"]
        assert len(data["candidates"]["rest_api"]) == 1
    
    def test_get_component_candidates_empty(self, client, sample_project_state):
        """Test getting candidates when none exist"""
        session_manager._active_sessions[sample_project_state.session_id] = sample_project_state
        
        response = client.get(f"/api/projects/{sample_project_state.session_id}/components/candidates")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["success"] is True
        assert data["candidates"] == {}
        assert "No component candidates found" in data["message"]
    
    def test_select_component_success(self, client, sample_project_state):
        """Test successful component selection"""
        # Setup project with candidate components
        candidate_data = {
            "candidate_repo": {
                "name": "fastapi-advanced",
                "full_name": "awesome/fastapi-advanced",
                "stars": 5000
            },
            "health_report": {
                "health_score": 85
            },
            "recommendation_rank": 1
        }
        
        sample_project_state.candidate_components = {
            "rest_api": [candidate_data]
        }
        session_manager._active_sessions[sample_project_state.session_id] = sample_project_state

        response = client.post(
            f"/api/projects/{sample_project_state.session_id}/components/select",
            json={
                "pattern_name": "rest_api",
                "component_full_name": "awesome/fastapi-advanced"
            }
        )

        assert response.status_code == 200
        data = response.json()

        assert data["success"] is True
        assert data["selected_component"] is not None
        assert data["selected_component"]["candidate_repo"]["name"] == "fastapi-advanced"

        # Check that project state was updated
        updated_project = session_manager.get_session(sample_project_state.session_id)
        assert "rest_api" in updated_project.selected_components
        assert updated_project.selected_components["rest_api"]["candidate_repo"]["name"] == "fastapi-advanced"
    
    def test_select_component_not_found(self, client, sample_project_state):
        """Test component selection with non-existent component"""
        sample_project_state.candidate_components = {
            "rest_api": []
        }
        session_manager._active_sessions[sample_project_state.session_id] = sample_project_state

        response = client.post(
            f"/api/projects/{sample_project_state.session_id}/components/select",
            json={
                "pattern_name": "rest_api",
                "component_full_name": "nonexistent/component"
            }
        )

        assert response.status_code == 404
        assert "not found in candidates" in response.json()["detail"]
    
    def test_select_component_custom_url_not_implemented(self, client, sample_project_state):
        """Test component selection with custom URL (not yet implemented)"""
        session_manager._active_sessions[sample_project_state.session_id] = sample_project_state

        response = client.post(
            f"/api/projects/{sample_project_state.session_id}/components/select",
            json={
                "pattern_name": "rest_api",
                "component_full_name": "custom/component",
                "custom_component_url": "https://github.com/custom/component"
            }
        )

        assert response.status_code == 501
        assert "not yet implemented" in response.json()["detail"]
    
    def test_get_selected_components_success(self, client, sample_project_state):
        """Test getting selected components"""
        # Add selected components to project state
        sample_project_state.selected_components = {
            "rest_api": {
                "candidate_repo": {
                    "name": "fastapi-advanced",
                    "full_name": "awesome/fastapi-advanced"
                }
            }
        }
        session_manager._active_sessions[sample_project_state.session_id] = sample_project_state

        response = client.get(f"/api/projects/{sample_project_state.session_id}/components/selected")

        assert response.status_code == 200
        data = response.json()

        assert data["success"] is True
        assert "selected_components" in data
        assert "rest_api" in data["selected_components"]
        assert data["metadata"]["selection_count"] == 1
    
    def test_get_selected_components_empty(self, client, sample_project_state):
        """Test getting selected components when none exist"""
        session_manager._active_sessions[sample_project_state.session_id] = sample_project_state

        response = client.get(f"/api/projects/{sample_project_state.session_id}/components/selected")

        assert response.status_code == 200
        data = response.json()

        assert data["success"] is True
        assert data["selected_components"] == {}
        assert data["metadata"]["selection_count"] == 0
    
    def test_deselect_component_success(self, client, sample_project_state):
        """Test successful component deselection"""
        # Setup project with selected component
        sample_project_state.selected_components = {
            "rest_api": {
                "candidate_repo": {
                    "name": "fastapi-advanced"
                }
            }
        }
        sample_project_state.status = ProjectStatus.QUILTING
        session_manager._active_sessions[sample_project_state.session_id] = sample_project_state

        response = client.delete(f"/api/projects/{sample_project_state.session_id}/components/rest_api/selection")

        assert response.status_code == 200
        data = response.json()

        assert data["success"] is True
        assert "removed" in data["message"]

        # Check that component was removed and status updated
        updated_project = session_manager.get_session(sample_project_state.session_id)
        assert "rest_api" not in updated_project.selected_components
        assert updated_project.status == ProjectStatus.PROCURING
    
    def test_deselect_component_not_selected(self, client, sample_project_state):
        """Test deselecting component that wasn't selected"""
        session_manager._active_sessions[sample_project_state.session_id] = sample_project_state

        response = client.delete(f"/api/projects/{sample_project_state.session_id}/components/rest_api/selection")

        assert response.status_code == 200
        data = response.json()

        assert data["success"] is True
        assert "No component was selected" in data["message"]
    
    def test_project_status_transitions(self, client, sample_project_state, sample_brainstorming_session):
        """Test that project status transitions correctly through component discovery"""
        # Setup initial state
        session_manager._active_sessions[sample_project_state.session_id] = sample_project_state
        brainstorming_sessions[sample_brainstorming_session.session_id] = sample_brainstorming_session
        
        # 1. Search for components (should move to PROCURING)
        search_response = client.post(
            f"/api/projects/{sample_project_state.session_id}/components/search",
            json={
                "brainstorming_session_id": sample_brainstorming_session.session_id,
                "max_recommendations": 3,
                "use_real_apis": False
            }
        )
        
        assert search_response.status_code == 200
        project = session_manager.get_session(sample_project_state.session_id)
        assert project.status == ProjectStatus.PROCURING
        
        # 2. Select a component (should move to QUILTING when all patterns have selections)
        # First, get the available candidates
        candidates_response = client.get(f"/api/projects/{sample_project_state.session_id}/components/candidates")
        candidates = candidates_response.json()["candidates"]
        
        # Select component for each pattern
        for pattern_name, pattern_candidates in candidates.items():
            if pattern_candidates:
                first_candidate = pattern_candidates[0]
                component_name = first_candidate["candidate_repo"]["full_name"]
                
                select_response = client.post(
                    f"/api/projects/{sample_project_state.session_id}/components/select",
                    json={
                        "pattern_name": pattern_name,
                        "component_full_name": component_name
                    }
                )
                assert select_response.status_code == 200
        
        # Check final status
        final_project = session_manager.get_session(sample_project_state.session_id)
        assert final_project.status == ProjectStatus.QUILTING
    
    def teardown_method(self):
        """Clean up after each test"""
        session_manager._active_sessions.clear()
        brainstorming_sessions.clear()
