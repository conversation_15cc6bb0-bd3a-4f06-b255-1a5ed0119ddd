"""
Tests for the Brainstorming API endpoints.

Comprehensive test suite covering all brainstorming API functionality including
session management, question answering, and structured brief generation.
"""

import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import AsyncMock, patch

from backend.src.main import app
from backend.src.api.brainstorming import brainstorming_sessions


class TestBrainstormingAPI:
    """Test brainstorming API endpoints"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.client = TestClient(app)
        # Clear sessions before each test
        brainstorming_sessions.clear()
    
    def test_start_brainstorming_success(self):
        """Test starting a new brainstorming session"""
        session_id = "test_session_1"
        request_data = {
            "initial_description": "A blog API for content management"
        }
        
        response = self.client.post(
            f"/api/projects/{session_id}/brainstorm/start",
            json=request_data
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["session_id"] == session_id
        assert "message" in data
        assert data["project_description"] == request_data["initial_description"]
        assert "next_questions" in data
        assert isinstance(data["next_questions"], list)
        assert len(data["next_questions"]) > 0
        assert "created_at" in data
        
        # Verify session was stored
        assert session_id in brainstorming_sessions
    
    def test_start_brainstorming_empty_description(self):
        """Test starting brainstorming with empty description"""
        session_id = "test_session_2"
        request_data = {"initial_description": ""}
        
        response = self.client.post(
            f"/api/projects/{session_id}/brainstorm/start",
            json=request_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["project_description"] == ""
        assert len(data["next_questions"]) > 0
    
    def test_start_brainstorming_duplicate_session(self):
        """Test starting brainstorming with existing session ID"""
        session_id = "test_session_3"
        request_data = {"initial_description": "Test project"}
        
        # Start first session
        response1 = self.client.post(
            f"/api/projects/{session_id}/brainstorm/start",
            json=request_data
        )
        assert response1.status_code == 200
        
        # Try to start duplicate session
        response2 = self.client.post(
            f"/api/projects/{session_id}/brainstorm/start",
            json=request_data
        )
        assert response2.status_code == 409
        assert "already exists" in response2.json()["detail"]
    
    def test_submit_answer_success(self):
        """Test submitting an answer to a question"""
        session_id = "test_session_4"
        
        # Start session first
        start_response = self.client.post(
            f"/api/projects/{session_id}/brainstorm/start",
            json={"initial_description": "Test API"}
        )
        assert start_response.status_code == 200
        
        # Submit answer
        answer_data = {
            "question_id": "communication_style",
            "answer": "Web API (REST/GraphQL)",
            "confidence": 0.9
        }
        
        response = self.client.post(
            f"/api/projects/{session_id}/brainstorm/answer",
            json=answer_data
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["session_id"] == session_id
        assert data["answer_processed"] is True
        assert "conversation_complete" in data
        assert "next_questions" in data
        assert "pattern_confidences" in data
        assert "intelligent_decisions" in data
        assert data["responses_count"] == 1
        assert "last_updated" in data
    
    def test_submit_answer_invalid_question(self):
        """Test submitting answer for invalid question"""
        session_id = "test_session_5"
        
        # Start session first
        self.client.post(
            f"/api/projects/{session_id}/brainstorm/start",
            json={"initial_description": "Test"}
        )
        
        # Submit answer with invalid question ID
        answer_data = {
            "question_id": "invalid_question_id",
            "answer": "Some answer",
            "confidence": 1.0
        }
        
        response = self.client.post(
            f"/api/projects/{session_id}/brainstorm/answer",
            json=answer_data
        )
        
        assert response.status_code == 400
        assert "Unknown question ID" in response.json()["detail"]
    
    def test_submit_answer_nonexistent_session(self):
        """Test submitting answer for nonexistent session"""
        answer_data = {
            "question_id": "communication_style",
            "answer": "Web API",
            "confidence": 1.0
        }
        
        response = self.client.post(
            "/api/projects/nonexistent_session/brainstorm/answer",
            json=answer_data
        )
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    def test_get_brainstorming_status(self):
        """Test getting brainstorming session status"""
        session_id = "test_session_6"
        
        # Start session
        self.client.post(
            f"/api/projects/{session_id}/brainstorm/start",
            json={"initial_description": "Test project"}
        )
        
        # Submit an answer
        self.client.post(
            f"/api/projects/{session_id}/brainstorm/answer",
            json={
                "question_id": "communication_style",
                "answer": "Web API (REST/GraphQL)",
                "confidence": 0.8
            }
        )
        
        # Get status
        response = self.client.get(f"/api/projects/{session_id}/brainstorm/status")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["session_id"] == session_id
        assert "progress" in data
        assert 0 <= data["progress"] <= 1
        assert data["responses_count"] == 1
        assert "patterns_identified" in data
        assert "decisions_made" in data
        assert "conversation_complete" in data
        assert "next_questions" in data
        assert "last_updated" in data
    
    def test_get_status_nonexistent_session(self):
        """Test getting status for nonexistent session"""
        response = self.client.get("/api/projects/nonexistent/brainstorm/status")
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    def test_complete_brainstorming(self):
        """Test completing brainstorming and getting structured brief"""
        session_id = "test_session_7"
        
        # Start session and submit several answers
        self.client.post(
            f"/api/projects/{session_id}/brainstorm/start",
            json={"initial_description": "Blog API project"}
        )
        
        # Submit multiple answers
        answers = [
            ("communication_style", "Web API (REST/GraphQL)"),
            ("deployment_target", "Cloud Server / Virtual Machine"),
            ("expected_scale", "Medium (100s-1000s, startup/small business)"),
            ("data_persistence", "yes")
        ]
        
        for question_id, answer in answers:
            self.client.post(
                f"/api/projects/{session_id}/brainstorm/answer",
                json={"question_id": question_id, "answer": answer, "confidence": 0.9}
            )
        
        # Complete brainstorming
        response = self.client.post(f"/api/projects/{session_id}/brainstorm/complete")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "project_overview" in data
        assert "recommended_patterns" in data
        assert "intelligent_decisions" in data
        assert "user_responses" in data
        assert "next_steps" in data
        
        # Verify project overview
        assert "description" in data["project_overview"]
        assert "created_at" in data["project_overview"]
        assert "total_responses" in data["project_overview"]
        
        # Verify recommended patterns
        assert isinstance(data["recommended_patterns"], list)
        
        # Verify next steps
        assert isinstance(data["next_steps"], list)
        assert len(data["next_steps"]) > 0
    
    def test_get_available_questions(self):
        """Test getting all available questions"""
        session_id = "test_session_8"
        
        # Start session first
        self.client.post(
            f"/api/projects/{session_id}/brainstorm/start",
            json={"initial_description": "Test"}
        )
        
        response = self.client.get(f"/api/projects/{session_id}/brainstorm/questions")
        
        assert response.status_code == 200
        questions = response.json()
        
        assert isinstance(questions, list)
        assert len(questions) > 0
        
        # Verify question structure
        question = questions[0]
        assert "id" in question
        assert "text" in question
        assert "question_type" in question
        assert "category" in question
        assert "options" in question
        assert "required" in question
        assert "help_text" in question
    
    def test_delete_brainstorming_session(self):
        """Test deleting a brainstorming session"""
        session_id = "test_session_9"
        
        # Start session
        self.client.post(
            f"/api/projects/{session_id}/brainstorm/start",
            json={"initial_description": "Test"}
        )
        
        # Verify session exists
        assert session_id in brainstorming_sessions
        
        # Delete session
        response = self.client.delete(f"/api/projects/{session_id}/brainstorm")
        
        assert response.status_code == 200
        data = response.json()
        assert "deleted successfully" in data["message"]
        
        # Verify session was removed
        assert session_id not in brainstorming_sessions
    
    def test_delete_nonexistent_session(self):
        """Test deleting nonexistent session"""
        response = self.client.delete("/api/projects/nonexistent/brainstorm")
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]


class TestBrainstormingAPIIntegration:
    """Integration tests for complete brainstorming workflows"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.client = TestClient(app)
        brainstorming_sessions.clear()
    
    def test_complete_brainstorming_workflow(self):
        """Test a complete brainstorming workflow from start to finish"""
        session_id = "integration_test"
        
        # 1. Start brainstorming
        start_response = self.client.post(
            f"/api/projects/{session_id}/brainstorm/start",
            json={"initial_description": "A REST API for a blog platform"}
        )
        assert start_response.status_code == 200
        start_data = start_response.json()
        assert len(start_data["next_questions"]) > 0
        
        # 2. Answer questions progressively
        question_answers = [
            ("project_description", "A REST API for a blog platform"),
            ("communication_style", "Web API (REST/GraphQL)"),
            ("deployment_target", "Cloud Server / Virtual Machine"),
            ("expected_scale", "Medium (100s-1000s, startup/small business)"),
            ("data_persistence", "yes")
        ]
        
        for question_id, answer in question_answers:
            answer_response = self.client.post(
                f"/api/projects/{session_id}/brainstorm/answer",
                json={"question_id": question_id, "answer": answer, "confidence": 0.9}
            )
            
            if answer_response.status_code == 200:  # Valid question
                answer_data = answer_response.json()
                assert answer_data["answer_processed"] is True
                assert "pattern_confidences" in answer_data
        
        # 3. Check status
        status_response = self.client.get(f"/api/projects/{session_id}/brainstorm/status")
        assert status_response.status_code == 200
        status_data = status_response.json()
        assert status_data["responses_count"] > 0
        assert status_data["progress"] > 0
        
        # 4. Complete brainstorming
        complete_response = self.client.post(f"/api/projects/{session_id}/brainstorm/complete")
        assert complete_response.status_code == 200
        complete_data = complete_response.json()
        
        # Verify structured brief
        assert complete_data["project_overview"]["total_responses"] > 0
        assert len(complete_data["recommended_patterns"]) >= 0
        assert len(complete_data["next_steps"]) > 0
        
        # 5. Clean up
        delete_response = self.client.delete(f"/api/projects/{session_id}/brainstorm")
        assert delete_response.status_code == 200
