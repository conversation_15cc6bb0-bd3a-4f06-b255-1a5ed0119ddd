"""
Tests for GitHub search integration.

Tests the dependency injection pattern, mock client behavior,
and search functionality for different architectural patterns.
"""

import pytest
import os
from unittest.mock import patch

from backend.src.modules.github_search import (
    MockGitHubClient, RealGitHubClient, create_github_client
)
from backend.src.integrations.base_client import APIResponse


class TestMockGitHubClient:
    """Test MockGitHubClient functionality"""
    
    @pytest.fixture
    def mock_client(self):
        """Create MockGitHubClient for testing"""
        return MockGitHubClient()
    
    @pytest.mark.asyncio
    async def test_connection_test(self, mock_client):
        """Test mock client connection"""
        response = await mock_client.test_connection()
        
        assert response.success is True
        assert response.data["status"] == "connected"
        assert response.data["client_type"] == "mock"
    
    @pytest.mark.asyncio
    async def test_search_repositories_rest_api(self, mock_client):
        """Test repository search for REST API pattern"""
        requirements = {
            "language": "Python",
            "communication": "Web API (REST/GraphQL)"
        }
        
        response = await mock_client.search_repositories_for_pattern(
            "rest_api", requirements, max_results=5
        )
        
        assert response.success is True
        assert "total_count" in response.data
        assert "items" in response.data
        assert len(response.data["items"]) > 0
        
        # Check that results contain relevant repositories
        items = response.data["items"]
        repo_names = [item["name"] for item in items]
        repo_descriptions = " ".join([item["description"] for item in items])
        
        # Should contain API-related repositories
        assert any("api" in name.lower() for name in repo_names) or "api" in repo_descriptions.lower()
    
    @pytest.mark.asyncio
    async def test_search_repositories_message_queue(self, mock_client):
        """Test repository search for message queue pattern"""
        requirements = {
            "language": "Python",
            "scale": "High"
        }
        
        response = await mock_client.search_repositories_for_pattern(
            "message_queue", requirements, max_results=5
        )
        
        assert response.success is True
        assert len(response.data["items"]) > 0
        
        # Check that results contain queue-related repositories
        items = response.data["items"]
        repo_text = " ".join([f"{item['name']} {item['description']}" for item in items]).lower()
        
        assert any(keyword in repo_text for keyword in ["queue", "celery", "redis", "task"])
    
    @pytest.mark.asyncio
    async def test_search_repositories_language_filter(self, mock_client):
        """Test language filtering in repository search"""
        requirements = {
            "language": "Python"
        }
        
        response = await mock_client.search_repositories_for_pattern(
            "rest_api", requirements, max_results=10
        )
        
        assert response.success is True
        
        # All returned repositories should be Python
        for item in response.data["items"]:
            assert item["language"] == "Python"
    
    @pytest.mark.asyncio
    async def test_search_repositories_codequilter_decide(self, mock_client):
        """Test search when user chooses 'Let CodeQuilter decide'"""
        requirements = {
            "language": "Let CodeQuilter decide based on project requirements"
        }
        
        response = await mock_client.search_repositories_for_pattern(
            "rest_api", requirements, max_results=5
        )
        
        assert response.success is True
        assert len(response.data["items"]) > 0
        
        # Should return repositories regardless of language
        languages = [item["language"] for item in response.data["items"]]
        assert len(set(languages)) >= 1  # At least one language represented
    
    @pytest.mark.asyncio
    async def test_get_repository_details(self, mock_client):
        """Test getting detailed repository information"""
        # Use a repository that should exist in mock data
        response = await mock_client.get_repository_details("awesome-org/fastapi-advanced")
        
        assert response.success is True
        assert response.data["full_name"] == "awesome-org/fastapi-advanced"
        assert "stargazers_count" in response.data
        assert "language" in response.data
    
    @pytest.mark.asyncio
    async def test_get_repository_details_not_found(self, mock_client):
        """Test getting details for non-existent repository"""
        response = await mock_client.get_repository_details("nonexistent/repo")
        
        assert response.success is False
        assert response.status_code == 404
        assert "not found" in response.error_message.lower()
    
    @pytest.mark.asyncio
    async def test_get_license_info(self, mock_client):
        """Test getting license information"""
        response = await mock_client.get_license_info("awesome-org/fastapi-advanced")
        
        assert response.success is True
        assert "license" in response.data
        assert "key" in response.data["license"]
    
    @pytest.mark.asyncio
    async def test_get_license_info_not_found(self, mock_client):
        """Test getting license for non-existent repository"""
        response = await mock_client.get_license_info("nonexistent/repo")
        
        assert response.success is False
        assert response.status_code == 404
    
    def test_pattern_keyword_mapping(self, mock_client):
        """Test that pattern keywords are correctly mapped"""
        # Test different patterns return different repositories
        rest_repos = mock_client._get_repos_for_pattern("rest_api")
        queue_repos = mock_client._get_repos_for_pattern("message_queue")
        gateway_repos = mock_client._get_repos_for_pattern("api_gateway")
        
        assert len(rest_repos) > 0
        assert len(queue_repos) > 0
        assert len(gateway_repos) > 0
        
        # Repositories should be different for different patterns
        rest_names = [repo["name"] for repo in rest_repos]
        queue_names = [repo["name"] for repo in queue_repos]
        
        # There should be some difference in results
        assert rest_names != queue_names or len(set(rest_names + queue_names)) > len(rest_names)
    
    def test_mock_repository_data_quality(self, mock_client):
        """Test that mock repository data has required fields"""
        repos = mock_client._mock_repositories
        
        assert len(repos) >= 5  # Should have multiple test repositories
        
        for repo in repos:
            # Check required fields
            required_fields = [
                "name", "full_name", "description", "html_url", "clone_url",
                "stargazers_count", "forks_count", "watchers_count", "open_issues_count",
                "language", "topics", "created_at", "updated_at", "pushed_at"
            ]
            
            for field in required_fields:
                assert field in repo, f"Missing field {field} in repository {repo.get('name', 'unknown')}"
            
            # Check data types
            assert isinstance(repo["stargazers_count"], int)
            assert isinstance(repo["topics"], list)
            assert isinstance(repo["archived"], bool)


class TestRealGitHubClient:
    """Test RealGitHubClient (currently returns not implemented)"""
    
    @pytest.fixture
    def real_client(self):
        """Create RealGitHubClient for testing"""
        return RealGitHubClient()
    
    @pytest.mark.asyncio
    async def test_real_client_not_implemented(self, real_client):
        """Test that real client returns not implemented errors"""
        response = await real_client.test_connection()
        
        assert response.success is False
        assert response.status_code == 501
        assert "not yet implemented" in response.error_message.lower()
    
    @pytest.mark.asyncio
    async def test_search_not_implemented(self, real_client):
        """Test that search returns not implemented"""
        response = await real_client.search_repositories_for_pattern(
            "rest_api", {}, max_results=5
        )
        
        assert response.success is False
        assert response.status_code == 501


class TestGitHubClientFactory:
    """Test GitHub client factory function"""
    
    def test_create_mock_client_default(self):
        """Test creating mock client by default"""
        client = create_github_client(use_real_api=False)
        
        assert isinstance(client, MockGitHubClient)
    
    @patch.dict(os.environ, {}, clear=True)
    def test_create_real_client_no_token(self):
        """Test creating real client without GitHub token falls back to mock"""
        with patch('builtins.print') as mock_print:
            client = create_github_client(use_real_api=True)
            
            assert isinstance(client, MockGitHubClient)
            # Should print warning about missing token
            mock_print.assert_called()
            warning_calls = [call for call in mock_print.call_args_list if "GITHUB_TOKEN not found" in str(call)]
            assert len(warning_calls) > 0
    
    @patch.dict(os.environ, {"GITHUB_TOKEN": "test_token"})
    def test_create_real_client_with_token(self):
        """Test creating real client with GitHub token"""
        with patch('builtins.print') as mock_print:
            client = create_github_client(use_real_api=True)
            
            assert isinstance(client, RealGitHubClient)
            # Should print message about creating real client
            mock_print.assert_called()
            real_client_calls = [call for call in mock_print.call_args_list if "real GitHub API client" in str(call)]
            assert len(real_client_calls) > 0
    
    def test_create_mock_client_explicit(self):
        """Test explicitly creating mock client"""
        with patch('builtins.print') as mock_print:
            client = create_github_client(use_real_api=False)
            
            assert isinstance(client, MockGitHubClient)
            # Should print message about creating mock client
            mock_print.assert_called()
            mock_client_calls = [call for call in mock_print.call_args_list if "mock GitHub client" in str(call)]
            assert len(mock_client_calls) > 0


class TestGitHubSearchIntegration:
    """Integration tests for GitHub search functionality"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_search_flow(self):
        """Test complete search flow from pattern to candidates"""
        client = create_github_client(use_real_api=False)
        
        # Simulate brainstorming requirements
        requirements = {
            "language": "Python",
            "communication": "Web API (REST/GraphQL)",
            "scale": "Medium (100s-1000s, startup/small business)"
        }
        
        # Search for REST API components
        response = await client.search_repositories_for_pattern(
            "rest_api", requirements, max_results=3
        )
        
        assert response.success is True
        assert len(response.data["items"]) <= 3
        
        # Verify repository structure
        for repo in response.data["items"]:
            assert "name" in repo
            assert "full_name" in repo
            assert "stargazers_count" in repo
            assert repo["language"] == "Python"
    
    @pytest.mark.asyncio
    async def test_multiple_pattern_search(self):
        """Test searching for multiple patterns"""
        client = create_github_client(use_real_api=False)
        
        patterns = ["rest_api", "message_queue", "api_gateway"]
        requirements = {"language": "Python"}
        
        results = {}
        for pattern in patterns:
            response = await client.search_repositories_for_pattern(
                pattern, requirements, max_results=2
            )
            
            assert response.success is True
            results[pattern] = response.data["items"]
        
        # Should have results for all patterns
        assert len(results) == 3
        for pattern, repos in results.items():
            assert len(repos) > 0, f"No repositories found for pattern {pattern}"
