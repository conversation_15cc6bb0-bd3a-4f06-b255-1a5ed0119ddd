"""
Tests for ContextWeaver v2 - Enhanced Context Engine for Code Generation.

Tests the component-aware prompt building, curated knowledge base integration,
and RAG-enhanced generation capabilities.
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime

from backend.src.modules.code_generation.context_weaver import ContextWeaver
from backend.src.state.components import (
    ComponentAPI, GenerationContext, CodeGenerationType, 
    IntegrationComplexity, GitHubRepo
)
from backend.src.state.project_state import ProjectState, ProjectStatus


class TestContextWeaver:
    """Test ContextWeaver functionality"""
    
    @pytest.fixture
    def context_weaver(self):
        """Create ContextWeaver instance for testing"""
        return ContextWeaver()
    
    @pytest.fixture
    def sample_project_state(self):
        """Create sample project state with component data"""
        project_state = ProjectState(
            session_id="test-session",
            project_name="Test API Project",
            project_brief="A test REST API with authentication",
            target_patterns=["rest_api"],
            status=ProjectStatus.QUILTING
        )
        
        # Add component API data
        project_state.component_apis["fastapi-auth"] = {
            "component_name": "fastapi-auth",
            "language": "python",
            "exported_classes": [
                {"name": "AuthManager", "methods": [{"name": "authenticate", "signature": "def authenticate(token: str)"}]}
            ],
            "exported_functions": [
                {"name": "create_token", "signature": "def create_token(user_id: str)"}
            ],
            "imports": [{"type": "import", "module": "fastapi"}],
            "dependencies": ["fastapi", "pyjwt"],
            "entry_points": ["auth.py"],
            "api_patterns": ["fastapi", "jwt_auth"]
        }
        
        project_state.component_apis["user-service"] = {
            "component_name": "user-service",
            "language": "python",
            "exported_classes": [
                {"name": "UserService", "methods": [{"name": "get_user", "signature": "def get_user(user_id: int)"}]}
            ],
            "exported_functions": [],
            "imports": [{"type": "import", "module": "sqlalchemy"}],
            "dependencies": ["sqlalchemy", "pydantic"],
            "entry_points": ["user_service.py"],
            "api_patterns": ["sqlalchemy", "pydantic"]
        }
        
        # Add selected components
        project_state.selected_components["fastapi-auth"] = {
            "name": "fastapi-auth",
            "language": "Python",
            "pattern_match": "rest_api"
        }
        
        return project_state
    
    def test_build_generation_context_adapter(self, context_weaver, sample_project_state):
        """Test building generation context for adapter generation"""
        context = context_weaver.build_generation_context(
            goal="Create authentication adapter between FastAPI and user service",
            generation_type=CodeGenerationType.ADAPTER,
            project_state=sample_project_state,
            source_component="fastapi-auth",
            target_component="user-service"
        )
        
        # Verify context structure
        assert isinstance(context, GenerationContext)
        assert context.goal == "Create authentication adapter between FastAPI and user service"
        assert context.generation_type == CodeGenerationType.ADAPTER
        assert context.project_framework == "fastapi"
        assert context.project_language == "python"
        assert context.selected_pattern == "rest_api"
        
        # Verify component APIs are loaded
        assert context.source_component_api is not None
        assert context.source_component_api.component_name == "fastapi-auth"
        assert context.target_component_api is not None
        assert context.target_component_api.component_name == "user-service"
        
        # Verify context richness
        richness = context.get_context_richness_score()
        assert 0.0 <= richness <= 1.0
        assert richness > 0.5  # Should have good context with both APIs
    
    def test_build_generation_context_tests(self, context_weaver, sample_project_state):
        """Test building generation context for test generation"""
        context = context_weaver.build_generation_context(
            goal="Generate comprehensive tests for authentication adapter",
            generation_type=CodeGenerationType.TESTS,
            project_state=sample_project_state
        )
        
        assert context.generation_type == CodeGenerationType.TESTS
        assert context.preferred_model == "claude-3-haiku"
        assert context.include_tests is True
        assert len(context.pattern_templates) >= 0  # May have test templates
    
    def test_build_llm_prompt_comprehensive(self, context_weaver, sample_project_state):
        """Test building comprehensive LLM prompt"""
        context = context_weaver.build_generation_context(
            goal="Create authentication adapter",
            generation_type=CodeGenerationType.ADAPTER,
            project_state=sample_project_state,
            source_component="fastapi-auth",
            target_component="user-service"
        )
        
        prompt = context_weaver.build_llm_prompt(context)
        
        # Verify prompt structure
        assert isinstance(prompt, str)
        assert len(prompt) > 100  # Should be substantial
        
        # Verify key sections are included
        assert "CodeQuilter" in prompt
        assert "authentication adapter" in prompt.lower()
        assert "fastapi" in prompt.lower()
        assert "COMPONENT ANALYSIS" in prompt
        assert "GENERATION REQUEST" in prompt
        
        # Verify component information is included
        assert "fastapi-auth" in prompt
        assert "user-service" in prompt
        assert "AuthManager" in prompt
        assert "UserService" in prompt
    
    def test_build_llm_prompt_minimal_context(self, context_weaver):
        """Test building LLM prompt with minimal context"""
        minimal_state = ProjectState(
            session_id="minimal-test",
            project_name="Minimal Project",
            status=ProjectStatus.QUILTING
        )
        
        context = context_weaver.build_generation_context(
            goal="Create simple configuration",
            generation_type=CodeGenerationType.CONFIGURATION,
            project_state=minimal_state
        )
        
        prompt = context_weaver.build_llm_prompt(context)
        
        # Should still generate a valid prompt
        assert isinstance(prompt, str)
        assert len(prompt) > 50
        assert "configuration" in prompt.lower()
    
    def test_detect_project_framework(self, context_weaver, sample_project_state):
        """Test framework detection from project state"""
        framework = context_weaver._detect_project_framework(sample_project_state)
        assert framework == "fastapi"
        
        # Test with different component
        sample_project_state.selected_components["express-app"] = {
            "name": "express-middleware",
            "language": "JavaScript"
        }
        framework = context_weaver._detect_project_framework(sample_project_state)
        assert framework == "fastapi"  # Should still be fastapi (first match)
    
    def test_detect_project_language(self, context_weaver, sample_project_state):
        """Test language detection from project state"""
        language = context_weaver._detect_project_language(sample_project_state)
        assert language == "python"
    
    def test_determine_pattern(self, context_weaver, sample_project_state):
        """Test pattern determination logic"""
        pattern = context_weaver._determine_pattern(sample_project_state, CodeGenerationType.ADAPTER)
        assert pattern == "rest_api"
        
        pattern = context_weaver._determine_pattern(sample_project_state, CodeGenerationType.TESTS)
        assert pattern == "testing"
    
    def test_get_pattern_templates(self, context_weaver):
        """Test pattern template retrieval"""
        templates = context_weaver._get_pattern_templates("rest_api", CodeGenerationType.ADAPTER)
        assert isinstance(templates, list)
        
        # Should find FastAPI auth template
        if templates:
            assert any("FastAPI" in template for template in templates)
    
    def test_get_similar_examples(self, context_weaver):
        """Test RAG-enhanced example retrieval"""
        examples = context_weaver._get_similar_examples(
            "authentication adapter", 
            CodeGenerationType.ADAPTER, 
            "rest_api"
        )
        
        assert isinstance(examples, list)
        # Should find auth-related examples
        if examples:
            example = examples[0]
            assert "title" in example
            assert "description" in example
            assert "similarity_score" in example
            assert 0.0 <= example["similarity_score"] <= 1.0
    
    def test_select_optimal_model(self, context_weaver):
        """Test LLM model selection logic"""
        model = context_weaver._select_optimal_model(CodeGenerationType.ADAPTER)
        assert model == "deepseek-coder"
        
        model = context_weaver._select_optimal_model(CodeGenerationType.DOCUMENTATION)
        assert model == "claude-3-sonnet"
        
        model = context_weaver._select_optimal_model(CodeGenerationType.TESTS)
        assert model == "claude-3-haiku"
    
    def test_assess_complexity(self, context_weaver):
        """Test integration complexity assessment"""
        # Create context with component APIs
        context = GenerationContext(
            goal="Test complexity",
            generation_type=CodeGenerationType.ADAPTER,
            selected_pattern="rest_api"
        )
        
        # Add mock component APIs
        context.source_component_api = ComponentAPI(
            component_name="simple-component",
            language="python"
        )
        context.source_component_api.public_methods = [{"name": f"method_{i}"} for i in range(3)]
        
        complexity = context_weaver._assess_complexity(context)
        assert isinstance(complexity, IntegrationComplexity)
        assert complexity in [IntegrationComplexity.LOW, IntegrationComplexity.MEDIUM]
    
    def test_build_component_api_from_dict(self, context_weaver):
        """Test ComponentAPI object construction from dictionary"""
        api_data = {
            "component_name": "test-component",
            "language": "python",
            "exported_classes": [{"name": "TestClass"}],
            "dependencies": ["fastapi", "pydantic"]
        }
        
        api = context_weaver._build_component_api_from_dict(api_data)
        
        assert isinstance(api, ComponentAPI)
        assert api.component_name == "test-component"
        assert api.language == "python"
        assert len(api.exported_classes) == 1
        assert api.dependencies == ["fastapi", "pydantic"]
    
    def test_prompt_optimization(self, context_weaver):
        """Test prompt length optimization"""
        # Create a very long prompt
        long_prompt = "This is a test prompt. " * 1000
        
        optimized = context_weaver._optimize_prompt_length(long_prompt)
        
        # Should be shorter than original
        assert len(optimized) <= len(long_prompt)
        assert isinstance(optimized, str)
    
    def test_error_handling_in_context_building(self, context_weaver):
        """Test error handling during context building"""
        # Create invalid project state
        invalid_state = ProjectState(session_id="invalid")
        invalid_state.component_apis = {"broken": "not-a-dict"}
        
        # Should not raise exception
        context = context_weaver.build_generation_context(
            goal="Test error handling",
            generation_type=CodeGenerationType.ADAPTER,
            project_state=invalid_state
        )
        
        # Should return minimal context
        assert isinstance(context, GenerationContext)
        assert context.goal == "Test error handling"
        assert context.project_language == "python"  # Default fallback
    
    def test_knowledge_base_initialization(self, context_weaver):
        """Test knowledge base initialization"""
        kb = context_weaver.knowledge_base
        
        assert isinstance(kb, dict)
        assert "patterns" in kb
        assert "best_practices" in kb
        
        # Verify pattern templates exist
        assert "rest_api" in kb["patterns"]
        assert "message_queue" in kb["patterns"]
        
        # Verify best practices exist
        assert "error_handling" in kb["best_practices"]
        assert "testing" in kb["best_practices"]
        assert "security" in kb["best_practices"]
