"""
Tests for component discovery module.

Tests the core data models, ComponentMatcher orchestrator, and integration
with brainstorming results and project state.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock

from backend.src.modules.component_discovery import (
    CandidateRepo, HealthReport, RecommendedComponent,
    MaintenanceStatus, LicenseCompatibility, ComponentMatcher
)
from backend.src.modules.github_search import MockGitHubClient
from backend.src.modules.health_analysis import ComponentHealthAnalyzer
from backend.src.integrations.llm_client import LLMClient
from backend.src.integrations.base_client import APIResponse


class TestCandidateRepo:
    """Test CandidateRepo data model"""
    
    def test_candidate_repo_creation(self):
        """Test basic CandidateRepo creation"""
        now = datetime.now()
        
        repo = CandidateRepo(
            name="test-repo",
            full_name="owner/test-repo",
            description="Test repository",
            html_url="https://github.com/owner/test-repo",
            clone_url="https://github.com/owner/test-repo.git",
            stars=1000,
            forks=100,
            watchers=1000,
            open_issues=10,
            created_at=now - timedelta(days=365),
            updated_at=now - timedelta(days=1),
            pushed_at=now - timedelta(days=1),
            language="Python",
            topics=["api", "rest", "python"]
        )
        
        assert repo.name == "test-repo"
        assert repo.full_name == "owner/test-repo"
        assert repo.stars == 1000
        assert repo.language == "Python"
        assert "api" in repo.topics
    
    def test_candidate_repo_serialization(self):
        """Test CandidateRepo to_dict() method"""
        now = datetime.now()
        
        repo = CandidateRepo(
            name="test-repo",
            full_name="owner/test-repo",
            description="Test repository",
            html_url="https://github.com/owner/test-repo",
            clone_url="https://github.com/owner/test-repo.git",
            stars=1000,
            forks=100,
            watchers=1000,
            open_issues=10,
            created_at=now - timedelta(days=365),
            updated_at=now - timedelta(days=1),
            pushed_at=now - timedelta(days=1),
            language="Python"
        )
        
        data = repo.to_dict()
        
        assert data["name"] == "test-repo"
        assert data["stars"] == 1000
        assert data["language"] == "Python"
        assert "created_at" in data
        assert isinstance(data["created_at"], str)  # Should be ISO format
    
    def test_candidate_repo_from_github_data(self):
        """Test creating CandidateRepo from GitHub API response"""
        github_data = {
            "name": "fastapi-advanced",
            "full_name": "awesome-org/fastapi-advanced",
            "description": "Advanced FastAPI framework",
            "html_url": "https://github.com/awesome-org/fastapi-advanced",
            "clone_url": "https://github.com/awesome-org/fastapi-advanced.git",
            "stargazers_count": 15000,
            "forks_count": 1200,
            "watchers_count": 15000,
            "open_issues_count": 25,
            "language": "Python",
            "topics": ["fastapi", "api", "rest"],
            "created_at": "2022-01-15T10:30:00Z",
            "updated_at": "2024-12-01T15:45:00Z",
            "pushed_at": "2024-12-01T15:45:00Z",
            "license": {"key": "mit", "name": "MIT License"},
            "size": 2500,
            "default_branch": "main",
            "archived": False
        }
        
        repo = CandidateRepo.from_github_data(github_data)
        
        assert repo.name == "fastapi-advanced"
        assert repo.stars == 15000
        assert repo.language == "Python"
        assert repo.license_info["key"] == "mit"
        assert not repo.archived


class TestHealthReport:
    """Test HealthReport data model"""
    
    def test_health_report_creation(self):
        """Test basic HealthReport creation"""
        report = HealthReport(
            repo_name="owner/test-repo",
            health_score=85,
            activity_score=35.0,
            popularity_score=25.0,
            license_score=20.0,
            security_score=8.0,
            maintenance_status=MaintenanceStatus.ACTIVE,
            license_compatibility=LicenseCompatibility.COMPATIBLE,
            days_since_last_commit=5
        )
        
        assert report.repo_name == "owner/test-repo"
        assert report.health_score == 85
        assert report.maintenance_status == MaintenanceStatus.ACTIVE
        assert report.license_compatibility == LicenseCompatibility.COMPATIBLE
    
    def test_health_report_serialization(self):
        """Test HealthReport to_dict() method"""
        report = HealthReport(
            repo_name="owner/test-repo",
            health_score=85,
            activity_score=35.0,
            popularity_score=25.0,
            license_score=20.0,
            security_score=8.0,
            maintenance_status=MaintenanceStatus.ACTIVE,
            license_compatibility=LicenseCompatibility.COMPATIBLE,
            days_since_last_commit=5,
            risk_factors=["High open issues"],
            positive_indicators=["Recent activity", "Good license"]
        )
        
        data = report.to_dict()
        
        assert data["repo_name"] == "owner/test-repo"
        assert data["health_score"] == 85
        assert data["score_breakdown"]["activity"] == 35.0
        assert data["maintenance_status"] == "active"
        assert data["license_compatibility"] == "compatible"
        assert "High open issues" in data["risk_factors"]
        assert "Recent activity" in data["positive_indicators"]


class TestRecommendedComponent:
    """Test RecommendedComponent data model"""
    
    def test_recommended_component_creation(self):
        """Test basic RecommendedComponent creation"""
        # Create mock candidate repo
        now = datetime.now()
        candidate = CandidateRepo(
            name="test-repo",
            full_name="owner/test-repo",
            description="Test repository",
            html_url="https://github.com/owner/test-repo",
            clone_url="https://github.com/owner/test-repo.git",
            stars=1000,
            forks=100,
            watchers=1000,
            open_issues=10,
            created_at=now - timedelta(days=365),
            updated_at=now - timedelta(days=1),
            pushed_at=now - timedelta(days=1),
            language="Python"
        )
        
        # Create mock health report
        health_report = HealthReport(
            repo_name="owner/test-repo",
            health_score=85,
            activity_score=35.0,
            popularity_score=25.0,
            license_score=20.0,
            security_score=8.0,
            maintenance_status=MaintenanceStatus.ACTIVE,
            license_compatibility=LicenseCompatibility.COMPATIBLE,
            days_since_last_commit=5
        )
        
        # Create recommendation
        recommendation = RecommendedComponent(
            candidate_repo=candidate,
            health_report=health_report,
            llm_explanation="Excellent FastAPI framework with great community support",
            pros_and_cons={
                "pros": ["High performance", "Great documentation", "Active community"],
                "cons": ["Learning curve for beginners"]
            },
            integration_complexity="Low",
            integration_notes="Drop-in replacement for basic Flask apps",
            recommendation_rank=1,
            confidence_score=0.9
        )
        
        assert recommendation.recommendation_rank == 1
        assert recommendation.confidence_score == 0.9
        assert recommendation.integration_complexity == "Low"
        assert len(recommendation.pros_and_cons["pros"]) == 3
    
    def test_recommended_component_serialization(self):
        """Test RecommendedComponent to_dict() method"""
        # Create minimal objects for testing
        now = datetime.now()
        candidate = CandidateRepo(
            name="test-repo", full_name="owner/test-repo", description="Test",
            html_url="https://github.com/owner/test-repo", clone_url="https://github.com/owner/test-repo.git",
            stars=100, forks=10, watchers=100, open_issues=5,
            created_at=now, updated_at=now, pushed_at=now, language="Python"
        )
        
        health_report = HealthReport(
            repo_name="owner/test-repo", health_score=75, activity_score=30.0,
            popularity_score=20.0, license_score=15.0, security_score=7.0,
            maintenance_status=MaintenanceStatus.ACTIVE,
            license_compatibility=LicenseCompatibility.COMPATIBLE,
            days_since_last_commit=10
        )
        
        recommendation = RecommendedComponent(
            candidate_repo=candidate,
            health_report=health_report,
            llm_explanation="Good component",
            pros_and_cons={"pros": ["Fast"], "cons": ["Complex"]},
            integration_complexity="Medium",
            integration_notes="Requires configuration",
            recommendation_rank=2,
            confidence_score=0.8
        )
        
        data = recommendation.to_dict()
        
        assert "candidate_repo" in data
        assert "health_report" in data
        assert data["llm_explanation"] == "Good component"
        assert data["recommendation_rank"] == 2
        assert data["confidence_score"] == 0.8


class TestComponentMatcher:
    """Test ComponentMatcher orchestrator"""
    
    @pytest.fixture
    def mock_github_client(self):
        """Create mock GitHub client for testing"""
        return MockGitHubClient()
    
    @pytest.fixture
    def mock_llm_client(self):
        """Create mock LLM client for testing"""
        client = MagicMock(spec=LLMClient)
        client.generate_brainstorming_response = AsyncMock(return_value=APIResponse(
            success=True,
            data={
                "response": '''```json
                {
                  "explanation": "Excellent component for REST API pattern",
                  "pros_and_cons": {
                    "pros": ["High performance", "Great documentation"],
                    "cons": ["Learning curve"]
                  },
                  "integration_complexity": "Low",
                  "integration_notes": "Easy to integrate",
                  "confidence_score": 0.9
                }
                ```'''
            }
        ))
        return client
    
    @pytest.fixture
    def component_matcher(self, mock_github_client, mock_llm_client):
        """Create ComponentMatcher with mocked dependencies"""
        return ComponentMatcher(
            github_client=mock_github_client,
            llm_client=mock_llm_client,
            use_real_apis=False
        )
    
    @pytest.fixture
    def sample_brainstorming_results(self):
        """Sample brainstorming results for testing"""
        return {
            "pattern_scores": {
                "rest_api": {
                    "confidence": 0.85,
                    "reasoning": "Strong match for REST API pattern"
                }
            },
            "responses": [
                {
                    "question_id": "developer_preferences",
                    "answer": "Python"
                },
                {
                    "question_id": "communication_style",
                    "answer": "Web API (REST/GraphQL)"
                },
                {
                    "question_id": "expected_scale",
                    "answer": "Medium (100s-1000s, startup/small business)"
                }
            ]
        }
    
    @pytest.mark.asyncio
    async def test_find_best_components_success(self, component_matcher, sample_brainstorming_results):
        """Test successful component discovery"""
        recommendations = await component_matcher.find_best_components(
            sample_brainstorming_results, max_recommendations=3
        )
        
        assert isinstance(recommendations, dict)
        assert "rest_api" in recommendations
        assert len(recommendations["rest_api"]) > 0
        
        # Check first recommendation structure
        first_rec = recommendations["rest_api"][0]
        assert isinstance(first_rec, RecommendedComponent)
        assert first_rec.recommendation_rank == 1
        assert first_rec.candidate_repo.language == "Python"
    
    @pytest.mark.asyncio
    async def test_extract_requirements(self, component_matcher, sample_brainstorming_results):
        """Test requirement extraction from brainstorming results"""
        requirements = component_matcher._extract_requirements(sample_brainstorming_results)
        
        assert "rest_api" in requirements
        assert requirements["rest_api"]["confidence"] == 0.85
        assert requirements["rest_api"]["language"] == "Python"
        assert "Web API" in requirements["rest_api"]["communication"]
    
    @pytest.mark.asyncio
    async def test_search_candidates(self, component_matcher):
        """Test candidate search functionality"""
        requirements = {
            "language": "Python",
            "communication": "Web API (REST/GraphQL)"
        }
        
        candidates = await component_matcher._search_candidates("rest_api", requirements)
        
        assert isinstance(candidates, list)
        assert len(candidates) > 0
        
        # Check candidate structure
        first_candidate = candidates[0]
        assert isinstance(first_candidate, CandidateRepo)
        assert first_candidate.language == "Python"
    
    @pytest.mark.asyncio
    async def test_find_best_components_empty_results(self, component_matcher):
        """Test component discovery with empty brainstorming results"""
        empty_results = {
            "pattern_scores": {},
            "responses": []
        }
        
        recommendations = await component_matcher.find_best_components(empty_results)
        
        assert isinstance(recommendations, dict)
        assert len(recommendations) == 0
