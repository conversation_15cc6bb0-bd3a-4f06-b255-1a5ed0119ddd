"""
Tests for health analysis module.

Tests the quantifiable health scoring algorithm, maintenance status
determination, and risk/positive indicator identification.
"""

import pytest
from datetime import datetime, timedelta

from backend.src.modules.health_analysis import ComponentHealthAnalyzer
from backend.src.modules.component_discovery import (
    CandidateRepo, HealthReport, MaintenanceStatus, LicenseCompatibility
)


class TestComponentHealthAnalyzer:
    """Test ComponentHealthAnalyzer functionality"""
    
    @pytest.fixture
    def analyzer(self):
        """Create ComponentHealthAnalyzer for testing"""
        return ComponentHealthAnalyzer()
    
    @pytest.fixture
    def recent_active_repo(self):
        """Create a recently active, high-quality repository"""
        now = datetime.now()
        return CandidateRepo(
            name="active-repo",
            full_name="awesome/active-repo",
            description="Actively maintained repository",
            html_url="https://github.com/awesome/active-repo",
            clone_url="https://github.com/awesome/active-repo.git",
            stars=5000,
            forks=500,
            watchers=5000,
            open_issues=15,
            created_at=now - timedelta(days=500),
            updated_at=now - timedelta(days=1),
            pushed_at=now - timedelta(days=1),  # Very recent
            language="Python",
            topics=["api", "framework", "python"],
            license_info={"key": "mit", "name": "MIT License"}
        )
    
    @pytest.fixture
    def dormant_repo(self):
        """Create a dormant repository with issues"""
        now = datetime.now()
        return CandidateRepo(
            name="old-repo",
            full_name="legacy/old-repo",
            description="Old repository with issues",
            html_url="https://github.com/legacy/old-repo",
            clone_url="https://github.com/legacy/old-repo.git",
            stars=200,
            forks=20,
            watchers=200,
            open_issues=75,
            created_at=now - timedelta(days=1200),
            updated_at=now - timedelta(days=300),
            pushed_at=now - timedelta(days=300),  # Very old
            language="Python",
            topics=["legacy"],
            license_info={"key": "gpl-3.0", "name": "GNU General Public License v3.0"}
        )
    
    @pytest.fixture
    def no_license_repo(self):
        """Create a repository without license"""
        now = datetime.now()
        return CandidateRepo(
            name="no-license-repo",
            full_name="individual/no-license-repo",
            description="Repository without license",
            html_url="https://github.com/individual/no-license-repo",
            clone_url="https://github.com/individual/no-license-repo.git",
            stars=100,
            forks=10,
            watchers=100,
            open_issues=5,
            created_at=now - timedelta(days=200),
            updated_at=now - timedelta(days=30),
            pushed_at=now - timedelta(days=30),
            language="Python",
            topics=["utility"],
            license_info=None
        )
    
    @pytest.mark.asyncio
    async def test_analyze_high_quality_repo(self, analyzer, recent_active_repo):
        """Test analysis of high-quality, active repository"""
        report = await analyzer.analyze_repository_health(recent_active_repo)
        
        assert isinstance(report, HealthReport)
        assert report.repo_name == "awesome/active-repo"
        assert report.health_score >= 80  # Should be high quality
        assert report.maintenance_status == MaintenanceStatus.ACTIVE
        assert report.license_compatibility == LicenseCompatibility.COMPATIBLE
        assert report.days_since_last_commit <= 2
        
        # Should have positive indicators
        assert len(report.positive_indicators) > 0
        assert any("recent" in indicator.lower() for indicator in report.positive_indicators)
        
        # Should have minimal risk factors
        assert len(report.risk_factors) <= 2
    
    @pytest.mark.asyncio
    async def test_analyze_dormant_repo(self, analyzer, dormant_repo):
        """Test analysis of dormant repository with issues"""
        report = await analyzer.analyze_repository_health(dormant_repo)
        
        assert isinstance(report, HealthReport)
        assert report.repo_name == "legacy/old-repo"
        assert report.health_score < 60  # Should be lower quality
        assert report.maintenance_status == MaintenanceStatus.DORMANT
        assert report.license_compatibility == LicenseCompatibility.RESTRICTIVE
        assert report.days_since_last_commit >= 300
        
        # Should have risk factors
        assert len(report.risk_factors) > 0
        assert any("recent commits" in factor.lower() for factor in report.risk_factors)
        assert any("issues" in factor.lower() for factor in report.risk_factors)
    
    @pytest.mark.asyncio
    async def test_analyze_no_license_repo(self, analyzer, no_license_repo):
        """Test analysis of repository without license"""
        report = await analyzer.analyze_repository_health(no_license_repo)
        
        assert report.license_compatibility == LicenseCompatibility.UNKNOWN
        assert report.license_score == 0.0
        assert any("license" in factor.lower() for factor in report.risk_factors)
    
    def test_activity_score_calculation(self, analyzer):
        """Test activity score calculation with different commit ages"""
        now = datetime.now()
        
        # Test very recent commits (should get max score)
        recent_repo = CandidateRepo(
            name="test", full_name="test/test", description="", html_url="", clone_url="",
            stars=100, forks=10, watchers=100, open_issues=5,
            created_at=now - timedelta(days=100), updated_at=now, pushed_at=now - timedelta(days=1),
            language="Python"
        )
        
        score = analyzer._calculate_activity_score(recent_repo)
        assert score >= 35.0  # Should be high for recent commits
        
        # Test old commits (should get low score)
        old_repo = CandidateRepo(
            name="test", full_name="test/test", description="", html_url="", clone_url="",
            stars=100, forks=10, watchers=100, open_issues=5,
            created_at=now - timedelta(days=500), updated_at=now - timedelta(days=200),
            pushed_at=now - timedelta(days=200),
            language="Python"
        )
        
        old_score = analyzer._calculate_activity_score(old_repo)
        assert old_score < score  # Should be lower for old commits
        assert old_score >= 0.0   # Should not be negative
    
    def test_popularity_score_calculation(self, analyzer):
        """Test popularity score calculation with different metrics"""
        now = datetime.now()
        
        # Test high popularity repository
        popular_repo = CandidateRepo(
            name="test", full_name="test/test", description="", html_url="", clone_url="",
            stars=10000, forks=1000, watchers=10000, open_issues=20,
            created_at=now - timedelta(days=100), updated_at=now, pushed_at=now,
            language="Python"
        )
        
        popular_score = analyzer._calculate_popularity_score(popular_repo)
        
        # Test low popularity repository
        unpopular_repo = CandidateRepo(
            name="test", full_name="test/test", description="", html_url="", clone_url="",
            stars=10, forks=1, watchers=10, open_issues=2,
            created_at=now - timedelta(days=100), updated_at=now, pushed_at=now,
            language="Python"
        )
        
        unpopular_score = analyzer._calculate_popularity_score(unpopular_repo)
        
        assert popular_score > unpopular_score
        assert popular_score <= 30.0  # Should not exceed max
        assert unpopular_score >= 0.0  # Should not be negative
    
    def test_license_score_calculation(self, analyzer):
        """Test license score calculation for different license types"""
        now = datetime.now()
        
        # Test compatible license
        mit_repo = CandidateRepo(
            name="test", full_name="test/test", description="", html_url="", clone_url="",
            stars=100, forks=10, watchers=100, open_issues=5,
            created_at=now, updated_at=now, pushed_at=now, language="Python",
            license_info={"key": "mit", "name": "MIT License"}
        )
        
        mit_score = analyzer._calculate_license_score(mit_repo)
        assert mit_score == 20.0  # Should get full points
        
        # Test restrictive license
        gpl_repo = CandidateRepo(
            name="test", full_name="test/test", description="", html_url="", clone_url="",
            stars=100, forks=10, watchers=100, open_issues=5,
            created_at=now, updated_at=now, pushed_at=now, language="Python",
            license_info={"key": "gpl-3.0", "name": "GNU General Public License v3.0"}
        )
        
        gpl_score = analyzer._calculate_license_score(gpl_repo)
        assert gpl_score == 10.0  # Should get partial points
        
        # Test no license
        no_license_repo = CandidateRepo(
            name="test", full_name="test/test", description="", html_url="", clone_url="",
            stars=100, forks=10, watchers=100, open_issues=5,
            created_at=now, updated_at=now, pushed_at=now, language="Python",
            license_info=None
        )
        
        no_license_score = analyzer._calculate_license_score(no_license_repo)
        assert no_license_score == 0.0  # Should get no points
    
    @pytest.mark.asyncio
    async def test_security_score_calculation(self, analyzer):
        """Test security score calculation (currently mock)"""
        now = datetime.now()
        
        # Test recent, popular repository (should get higher security score)
        secure_repo = CandidateRepo(
            name="test", full_name="test/test", description="", html_url="", clone_url="",
            stars=15000, forks=1000, watchers=15000, open_issues=10,
            created_at=now - timedelta(days=100), updated_at=now, pushed_at=now,
            language="Python"
        )
        
        secure_score = await analyzer._calculate_security_score(secure_repo)
        
        # Test old, inactive repository (should get lower security score)
        insecure_repo = CandidateRepo(
            name="test", full_name="test/test", description="", html_url="", clone_url="",
            stars=100, forks=10, watchers=100, open_issues=50,
            created_at=now - timedelta(days=1500), updated_at=now - timedelta(days=200),
            pushed_at=now - timedelta(days=200), language="Python"
        )
        
        insecure_score = await analyzer._calculate_security_score(insecure_repo)
        
        assert secure_score >= insecure_score
        assert 0.0 <= secure_score <= 10.0
        assert 0.0 <= insecure_score <= 10.0
    
    def test_maintenance_status_determination(self, analyzer):
        """Test maintenance status determination based on activity"""
        now = datetime.now()
        
        # Test active repository
        active_repo = CandidateRepo(
            name="test", full_name="test/test", description="", html_url="", clone_url="",
            stars=100, forks=10, watchers=100, open_issues=5,
            created_at=now - timedelta(days=100), updated_at=now, 
            pushed_at=now - timedelta(days=15),  # Recent activity
            language="Python"
        )
        
        status = analyzer._determine_maintenance_status(active_repo)
        assert status == MaintenanceStatus.ACTIVE
        
        # Test infrequent repository
        infrequent_repo = CandidateRepo(
            name="test", full_name="test/test", description="", html_url="", clone_url="",
            stars=100, forks=10, watchers=100, open_issues=5,
            created_at=now - timedelta(days=200), updated_at=now - timedelta(days=60),
            pushed_at=now - timedelta(days=60),  # Some activity
            language="Python"
        )
        
        status = analyzer._determine_maintenance_status(infrequent_repo)
        assert status == MaintenanceStatus.INFREQUENT
        
        # Test dormant repository
        dormant_repo = CandidateRepo(
            name="test", full_name="test/test", description="", html_url="", clone_url="",
            stars=100, forks=10, watchers=100, open_issues=5,
            created_at=now - timedelta(days=500), updated_at=now - timedelta(days=200),
            pushed_at=now - timedelta(days=200),  # Old activity
            language="Python"
        )
        
        status = analyzer._determine_maintenance_status(dormant_repo)
        assert status == MaintenanceStatus.DORMANT
    
    def test_risk_factor_identification(self, analyzer):
        """Test risk factor identification"""
        now = datetime.now()
        
        # Repository with multiple risk factors
        risky_repo = CandidateRepo(
            name="test", full_name="test/test", description="", html_url="", clone_url="",
            stars=50,  # Low stars
            forks=5, watchers=50, 
            open_issues=100,  # High issues
            created_at=now - timedelta(days=500), updated_at=now - timedelta(days=200),
            pushed_at=now - timedelta(days=200),  # Old commits
            language="Python",
            license_info=None,  # No license
            archived=True  # Archived
        )
        
        risks = analyzer._identify_risk_factors(risky_repo, 40)  # Low health score
        
        assert len(risks) > 0
        assert any("recent commits" in risk.lower() for risk in risks)
        assert any("issues" in risk.lower() for risk in risks)
        assert any("license" in risk.lower() for risk in risks)
        assert any("archived" in risk.lower() for risk in risks)
        assert any("health score" in risk.lower() for risk in risks)
    
    def test_positive_indicator_identification(self, analyzer):
        """Test positive indicator identification"""
        now = datetime.now()
        
        # Repository with multiple positive indicators
        good_repo = CandidateRepo(
            name="test", full_name="test/test", description="", html_url="", clone_url="",
            stars=5000,  # High stars
            forks=500, watchers=5000,
            open_issues=10,
            created_at=now - timedelta(days=200), updated_at=now,
            pushed_at=now - timedelta(days=3),  # Recent commits
            language="Python",
            topics=["api", "framework", "python", "web"],  # Many topics
            license_info={"key": "mit", "name": "MIT License"}
        )
        
        positives = analyzer._identify_positive_indicators(good_repo, 90)  # High health score
        
        assert len(positives) > 0
        assert any("recent" in positive.lower() for positive in positives)
        assert any("community" in positive.lower() for positive in positives)
        assert any("license" in positive.lower() for positive in positives)
        assert any("health score" in positive.lower() for positive in positives)
        assert any("topics" in positive.lower() for positive in positives)
    
    def test_custom_license_compatibility(self):
        """Test analyzer with custom license compatibility list"""
        custom_licenses = ["apache-2.0", "bsd-3-clause"]
        analyzer = ComponentHealthAnalyzer(compatible_licenses=custom_licenses)
        
        now = datetime.now()
        
        # Test Apache license (should be compatible)
        apache_repo = CandidateRepo(
            name="test", full_name="test/test", description="", html_url="", clone_url="",
            stars=100, forks=10, watchers=100, open_issues=5,
            created_at=now, updated_at=now, pushed_at=now, language="Python",
            license_info={"key": "apache-2.0", "name": "Apache License 2.0"}
        )
        
        apache_score = analyzer._calculate_license_score(apache_repo)
        assert apache_score == 20.0
        
        # Test MIT license (should not be compatible with custom list)
        mit_repo = CandidateRepo(
            name="test", full_name="test/test", description="", html_url="", clone_url="",
            stars=100, forks=10, watchers=100, open_issues=5,
            created_at=now, updated_at=now, pushed_at=now, language="Python",
            license_info={"key": "mit", "name": "MIT License"}
        )
        
        mit_score = analyzer._calculate_license_score(mit_repo)
        assert mit_score == 5.0  # Should get unknown license score
