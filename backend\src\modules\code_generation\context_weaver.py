"""
ContextWeaver v2 - Enhanced Context Engine for Code Generation.

Implements component-aware code generation with curated knowledge base integration
and RAG-enhanced generation capabilities. This is the intelligence layer that
builds rich context for LLM prompts to enable precise code generation.

Key Features:
- Component-aware prompt building using ComponentAPI analysis
- Curated knowledge base with pattern-specific templates
- RAG-enhanced generation with vector similarity search
- Intelligent context optimization for different generation tasks
"""

import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import asdict

from ...state.components import (
    ComponentAPI, GenerationContext, CodeGenerationType, 
    IntegrationComplexity, GitHubRepo
)
from ...state.project_state import ProjectState
from ...integrations.llm_client import LLMClient

logger = logging.getLogger(__name__)


class ContextWeaver:
    """
    Enhanced context engine for component-aware code generation.
    
    Builds intelligent prompts by combining component API analysis,
    curated knowledge base patterns, and RAG-enhanced examples.
    """
    
    def __init__(self, llm_client: Optional[LLMClient] = None):
        """Initialize ContextWeaver with LLM client for RAG capabilities"""
        # TODO: REPLACE_MOCK - LLM client integration for real RAG
        if llm_client is None:
            from ...integrations.llm_client import LLMClient
            self.llm_client = LLMClient()
        else:
            self.llm_client = llm_client
            
        # Initialize knowledge base
        self.knowledge_base = self._initialize_knowledge_base()
        
        # Context optimization settings
        self.max_context_tokens = 8000  # Conservative limit for most models
        self.context_priorities = {
            "component_apis": 0.4,
            "pattern_templates": 0.3,
            "similar_examples": 0.2,
            "project_context": 0.1
        }
    
    def build_generation_context(
        self,
        goal: str,
        generation_type: CodeGenerationType,
        project_state: ProjectState,
        source_component: Optional[str] = None,
        target_component: Optional[str] = None
    ) -> GenerationContext:
        """
        Build comprehensive generation context for code generation.
        
        Args:
            goal: High-level generation goal
            generation_type: Type of code generation (adapter, tests, etc.)
            project_state: Current project state with component data
            source_component: Source component name for adapter generation
            target_component: Target component name for adapter generation
            
        Returns:
            GenerationContext with all necessary information for code generation
        """
        logger.info(f"Building generation context for {generation_type.value}: {goal}")
        
        try:
            # Create base context
            context = GenerationContext(
                goal=goal,
                generation_type=generation_type,
                project_framework=self._detect_project_framework(project_state),
                project_language=self._detect_project_language(project_state)
            )
            
            # Add component API context
            if source_component and source_component in project_state.component_apis:
                context.source_component_api = self._build_component_api_from_dict(
                    project_state.component_apis[source_component]
                )
            
            if target_component and target_component in project_state.component_apis:
                context.target_component_api = self._build_component_api_from_dict(
                    project_state.component_apis[target_component]
                )
            
            # Add pattern context
            context.selected_pattern = self._determine_pattern(project_state, generation_type)
            
            # Add knowledge base context
            context.pattern_templates = self._get_pattern_templates(
                context.selected_pattern, generation_type
            )
            
            # Add RAG-enhanced examples
            context.similar_examples = self._get_similar_examples(
                goal, generation_type, context.selected_pattern
            )
            
            # Set generation preferences
            context.preferred_model = self._select_optimal_model(generation_type)
            context.max_complexity = self._assess_complexity(context)
            
            logger.info(f"Context built with richness score: {context.get_context_richness_score():.2f}")
            return context
            
        except Exception as e:
            logger.error(f"Error building generation context: {e}")
            # Return minimal context on error
            return GenerationContext(
                goal=goal,
                generation_type=generation_type,
                project_framework="unknown",
                project_language="python"  # Default fallback
            )
    
    def build_llm_prompt(self, context: GenerationContext) -> str:
        """
        Build optimized LLM prompt from generation context.
        
        Args:
            context: GenerationContext with all necessary information
            
        Returns:
            Optimized prompt string for LLM generation
        """
        logger.info(f"Building LLM prompt for {context.generation_type.value}")
        
        try:
            # Start with system prompt
            prompt_parts = [self._build_system_prompt(context)]
            
            # Add component context
            if context.source_component_api or context.target_component_api:
                prompt_parts.append(self._build_component_context(context))
            
            # Add pattern templates
            if context.pattern_templates:
                prompt_parts.append(self._build_pattern_context(context))
            
            # Add similar examples
            if context.similar_examples:
                prompt_parts.append(self._build_examples_context(context))
            
            # Add generation request
            prompt_parts.append(self._build_generation_request(context))
            
            # Combine and optimize
            full_prompt = "\n\n".join(prompt_parts)
            optimized_prompt = self._optimize_prompt_length(full_prompt)
            
            logger.info(f"Built prompt with {len(optimized_prompt)} characters")
            return optimized_prompt
            
        except Exception as e:
            logger.error(f"Error building LLM prompt: {e}")
            # Return basic prompt on error
            return f"Generate {context.generation_type.value} code for: {context.goal}"
    
    def _initialize_knowledge_base(self) -> Dict[str, Any]:
        """Initialize curated knowledge base with pattern templates"""
        # TODO: REPLACE_MOCK - Load from vector database or file system
        return {
            "patterns": {
                "rest_api": {
                    "fastapi_auth_adapter": {
                        "description": "FastAPI authentication adapter pattern",
                        "template": """
# FastAPI Authentication Adapter Template
from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
import jwt

class AuthAdapter:
    def __init__(self, secret_key: str):
        self.secret_key = secret_key
        self.security = HTTPBearer()
    
    async def verify_token(self, credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())):
        try:
            payload = jwt.decode(credentials.credentials, self.secret_key, algorithms=["HS256"])
            return payload
        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Token expired")
        except jwt.InvalidTokenError:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
""",
                        "complexity": "medium",
                        "dependencies": ["fastapi", "pyjwt"],
                        "test_template": """
import pytest
from fastapi.testclient import TestClient

def test_auth_adapter_valid_token():
    # Test implementation here
    pass

def test_auth_adapter_invalid_token():
    # Test implementation here
    pass
"""
                    }
                },
                "message_queue": {
                    "redis_adapter": {
                        "description": "Redis message queue adapter pattern",
                        "template": """
# Redis Message Queue Adapter Template
import redis
import json
from typing import Any, Optional

class RedisQueueAdapter:
    def __init__(self, redis_url: str):
        self.redis_client = redis.from_url(redis_url)
    
    async def publish(self, channel: str, message: Any):
        serialized = json.dumps(message)
        return self.redis_client.publish(channel, serialized)
    
    async def subscribe(self, channel: str):
        pubsub = self.redis_client.pubsub()
        pubsub.subscribe(channel)
        return pubsub
""",
                        "complexity": "low",
                        "dependencies": ["redis", "json"],
                        "test_template": """
import pytest
from unittest.mock import Mock

def test_redis_adapter_publish():
    # Test implementation here
    pass

def test_redis_adapter_subscribe():
    # Test implementation here
    pass
"""
                    }
                }
            },
            "best_practices": {
                "error_handling": [
                    "Always use try-catch blocks for external API calls",
                    "Provide meaningful error messages with context",
                    "Log errors with appropriate severity levels",
                    "Implement graceful degradation when possible"
                ],
                "testing": [
                    "Write unit tests for all public methods",
                    "Mock external dependencies in tests",
                    "Test both success and failure scenarios",
                    "Aim for >90% test coverage"
                ],
                "security": [
                    "Validate all input parameters",
                    "Use environment variables for secrets",
                    "Implement proper authentication and authorization",
                    "Sanitize data before database operations"
                ]
            }
        }

    def _detect_project_framework(self, project_state: ProjectState) -> str:
        """Detect primary framework from project state"""
        # Check selected components for framework indicators
        for component_name, component_data in project_state.selected_components.items():
            if isinstance(component_data, dict):
                name = component_data.get('name', '').lower()
                if 'fastapi' in name:
                    return 'fastapi'
                elif 'express' in name:
                    return 'express'
                elif 'spring' in name:
                    return 'spring'
                elif 'django' in name:
                    return 'django'

        # Check target patterns
        if 'rest_api' in project_state.target_patterns:
            return 'fastapi'  # Default for REST API
        elif 'message_queue' in project_state.target_patterns:
            return 'celery'   # Default for message queue

        return 'unknown'

    def _detect_project_language(self, project_state: ProjectState) -> str:
        """Detect primary language from project state"""
        # Check component APIs for language information
        for api_data in project_state.component_apis.values():
            if isinstance(api_data, dict) and 'language' in api_data:
                return api_data['language']

        # Check selected components
        for component_data in project_state.selected_components.values():
            if isinstance(component_data, dict) and 'language' in component_data:
                return component_data['language'].lower()

        return 'python'  # Default fallback

    def _build_component_api_from_dict(self, api_data: Dict[str, Any]) -> ComponentAPI:
        """Build ComponentAPI object from dictionary data"""
        # TODO: REPLACE_MOCK - Proper deserialization from ProjectState
        return ComponentAPI(
            component_name=api_data.get('component_name', ''),
            language=api_data.get('language', 'python'),
            public_methods=api_data.get('public_methods', []),
            exported_classes=api_data.get('exported_classes', []),
            exported_functions=api_data.get('exported_functions', []),
            imports=api_data.get('imports', []),
            exports=api_data.get('exports', []),
            dependencies=api_data.get('dependencies', []),
            config_options=api_data.get('config_options', {}),
            entry_points=api_data.get('entry_points', []),
            api_patterns=api_data.get('api_patterns', [])
        )

    def _determine_pattern(self, project_state: ProjectState, generation_type: CodeGenerationType) -> str:
        """Determine the most appropriate pattern for generation"""
        if generation_type == CodeGenerationType.ADAPTER:
            # For adapters, use the primary pattern from target patterns
            if project_state.target_patterns:
                return project_state.target_patterns[0]

        # Default patterns by generation type
        pattern_defaults = {
            CodeGenerationType.ADAPTER: "rest_api",
            CodeGenerationType.CONFIGURATION: "rest_api",
            CodeGenerationType.TESTS: "testing",
            CodeGenerationType.DOCUMENTATION: "documentation",
            CodeGenerationType.PROJECT_STRUCTURE: "project_setup"
        }

        return pattern_defaults.get(generation_type, "generic")

    def _get_pattern_templates(self, pattern: str, generation_type: CodeGenerationType) -> List[str]:
        """Get relevant pattern templates from knowledge base"""
        templates = []

        if pattern in self.knowledge_base["patterns"]:
            pattern_data = self.knowledge_base["patterns"][pattern]
            for template_name, template_info in pattern_data.items():
                if generation_type == CodeGenerationType.ADAPTER:
                    templates.append(template_info.get("template", ""))
                elif generation_type == CodeGenerationType.TESTS:
                    templates.append(template_info.get("test_template", ""))

        return [t for t in templates if t.strip()]

    def _get_similar_examples(self, goal: str, generation_type: CodeGenerationType, pattern: str) -> List[Dict[str, Any]]:
        """Get similar examples using RAG-enhanced search"""
        # TODO: REPLACE_MOCK - Implement vector similarity search
        examples = []

        # Mock examples based on goal keywords
        goal_lower = goal.lower()

        if "auth" in goal_lower and generation_type == CodeGenerationType.ADAPTER:
            examples.append({
                "title": "JWT Authentication Adapter",
                "description": "FastAPI JWT authentication with token validation",
                "code_snippet": "# JWT authentication adapter example...",
                "complexity": "medium",
                "similarity_score": 0.85
            })

        if "database" in goal_lower:
            examples.append({
                "title": "Database Connection Adapter",
                "description": "SQLAlchemy database adapter with connection pooling",
                "code_snippet": "# Database adapter example...",
                "complexity": "low",
                "similarity_score": 0.78
            })

        return examples

    def _select_optimal_model(self, generation_type: CodeGenerationType) -> str:
        """Select optimal LLM model based on generation type"""
        # Model selection strategy based on task complexity
        model_mapping = {
            CodeGenerationType.ADAPTER: "deepseek-coder",      # Fast, specialized for code
            CodeGenerationType.TESTS: "claude-3-haiku",       # Good at test generation
            CodeGenerationType.CONFIGURATION: "deepseek-coder", # Simple config generation
            CodeGenerationType.DOCUMENTATION: "claude-3-sonnet", # Excellent at documentation
            CodeGenerationType.PROJECT_STRUCTURE: "claude-3-sonnet" # Good at architecture
        }

        return model_mapping.get(generation_type, "auto")

    def _assess_complexity(self, context: GenerationContext) -> IntegrationComplexity:
        """Assess integration complexity based on context"""
        complexity_score = 0

        # Component API complexity
        if context.source_component_api:
            complexity_score += context.source_component_api.get_complexity_score() * 0.4
        if context.target_component_api:
            complexity_score += context.target_component_api.get_complexity_score() * 0.4

        # Pattern complexity
        if context.selected_pattern in ["message_queue", "api_gateway"]:
            complexity_score += 0.3
        elif context.selected_pattern in ["rest_api", "adapter"]:
            complexity_score += 0.1

        # Map score to complexity enum
        if complexity_score < 0.3:
            return IntegrationComplexity.LOW
        elif complexity_score < 0.6:
            return IntegrationComplexity.MEDIUM
        elif complexity_score < 0.8:
            return IntegrationComplexity.HIGH
        else:
            return IntegrationComplexity.VERY_HIGH

    def _build_system_prompt(self, context: GenerationContext) -> str:
        """Build system prompt based on generation context"""
        base_prompt = f"""You are CodeQuilter, an expert AI system integrator specializing in creating production-ready applications through intelligent component integration.

TASK: Generate {context.generation_type.value} code for: {context.goal}

PROJECT CONTEXT:
- Framework: {context.project_framework}
- Language: {context.project_language}
- Pattern: {context.selected_pattern}
- Quality Level: {context.quality_level}

GENERATION REQUIREMENTS:
- Follow established architectural patterns
- Include comprehensive error handling
- Add appropriate logging and monitoring
- Generate corresponding unit tests
- Use dependency injection where appropriate
- Follow language-specific best practices

QUALITY STANDARDS:
- Production-ready code with proper structure
- Comprehensive error handling and validation
- Clear, maintainable code with good documentation
- Security best practices implemented
- Performance considerations included"""

        # Add specific requirements based on generation type
        if context.generation_type == CodeGenerationType.ADAPTER:
            base_prompt += """

ADAPTER GENERATION SPECIFICS:
- Create clean interfaces between components
- Handle data transformation and validation
- Implement proper error propagation
- Include connection management and retry logic
- Add configuration options for flexibility"""

        elif context.generation_type == CodeGenerationType.TESTS:
            base_prompt += """

TEST GENERATION SPECIFICS:
- Generate comprehensive unit tests
- Include integration test scenarios
- Test both success and failure cases
- Mock external dependencies appropriately
- Aim for high test coverage (>90%)"""

        return base_prompt

    def _build_component_context(self, context: GenerationContext) -> str:
        """Build component context section of prompt"""
        context_parts = ["COMPONENT ANALYSIS:"]

        if context.source_component_api:
            api = context.source_component_api
            context_parts.append(f"""
SOURCE COMPONENT: {api.component_name}
- Language: {api.language}
- Classes: {len(api.exported_classes)} ({[c.get('name', 'Unknown') for c in api.exported_classes[:3]]})
- Functions: {len(api.exported_functions)} ({[f.get('name', 'Unknown') for f in api.exported_functions[:3]]})
- Entry Points: {api.entry_points}
- Patterns: {api.api_patterns}
- Dependencies: {api.dependencies[:5]}""")

        if context.target_component_api:
            api = context.target_component_api
            context_parts.append(f"""
TARGET COMPONENT: {api.component_name}
- Language: {api.language}
- Classes: {len(api.exported_classes)} ({[c.get('name', 'Unknown') for c in api.exported_classes[:3]]})
- Functions: {len(api.exported_functions)} ({[f.get('name', 'Unknown') for f in api.exported_functions[:3]]})
- Entry Points: {api.entry_points}
- Patterns: {api.api_patterns}
- Dependencies: {api.dependencies[:5]}""")

        return "\n".join(context_parts)

    def _build_pattern_context(self, context: GenerationContext) -> str:
        """Build pattern templates section of prompt"""
        if not context.pattern_templates:
            return ""

        context_parts = ["PATTERN TEMPLATES:"]

        for i, template in enumerate(context.pattern_templates[:2]):  # Limit to 2 templates
            context_parts.append(f"""
Template {i+1}:
{template[:1000]}...  # Truncated for brevity
""")

        return "\n".join(context_parts)

    def _build_examples_context(self, context: GenerationContext) -> str:
        """Build similar examples section of prompt"""
        if not context.similar_examples:
            return ""

        context_parts = ["SIMILAR EXAMPLES:"]

        for example in context.similar_examples[:3]:  # Limit to 3 examples
            context_parts.append(f"""
Example: {example.get('title', 'Unknown')}
Description: {example.get('description', 'No description')}
Complexity: {example.get('complexity', 'unknown')}
Similarity: {example.get('similarity_score', 0):.2f}
""")

        return "\n".join(context_parts)

    def _build_generation_request(self, context: GenerationContext) -> str:
        """Build the specific generation request"""
        request = f"""
GENERATION REQUEST:
Generate {context.generation_type.value} code that:
1. Achieves the goal: {context.goal}
2. Integrates the analyzed components appropriately
3. Follows the {context.selected_pattern} pattern
4. Meets {context.quality_level} quality standards
5. Includes comprehensive error handling and logging
"""

        if context.include_tests:
            request += "6. Includes corresponding unit tests\n"

        if context.include_documentation:
            request += "7. Includes clear documentation and usage examples\n"

        request += """
OUTPUT FORMAT:
- Provide complete, runnable code
- Include file structure and organization
- Add clear comments explaining key decisions
- Specify required dependencies
- Include setup and configuration instructions
"""

        return request

    def _optimize_prompt_length(self, prompt: str) -> str:
        """Optimize prompt length to fit within token limits"""
        # TODO: REPLACE_MOCK - Implement proper token counting and optimization

        # Simple length-based optimization for now
        if len(prompt) <= self.max_context_tokens * 4:  # Rough char-to-token ratio
            return prompt

        # Truncate sections in order of priority
        lines = prompt.split('\n')
        optimized_lines = []
        current_length = 0
        max_length = self.max_context_tokens * 4

        for line in lines:
            if current_length + len(line) > max_length:
                optimized_lines.append("... [Content truncated for length] ...")
                break
            optimized_lines.append(line)
            current_length += len(line)

        return '\n'.join(optimized_lines)
