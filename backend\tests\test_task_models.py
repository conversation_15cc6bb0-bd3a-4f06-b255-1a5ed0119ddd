"""
Tests for TaskExecutionAgent task models.

Tests the core data models including Task, TaskStatus, ExecutionResult,
and SuggestedAction with Gemini's three principles.
"""

import pytest
from datetime import datetime
from unittest.mock import patch

from backend.src.agents.task_models import (
    Task, TaskStatus, ExecutionResult, SuggestedAction, 
    SuggestedActionType
)


class TestTask:
    """Test the Task data model with activity instrumentation"""
    
    def test_task_creation(self):
        """Test basic task creation"""
        task = Task(description="Test task")
        
        assert task.description == "Test task"
        assert task.status == TaskStatus.PENDING
        assert task.current_activity is None
        assert task.start_time is None
        assert task.end_time is None
        assert task.error_message is None
        assert isinstance(task.metadata, dict)
        assert len(task.id) > 0  # UUID generated
    
    def test_task_lifecycle_start(self):
        """Test task start with activity"""
        task = Task(description="Test task")
        
        with patch('backend.src.agents.task_models.datetime') as mock_datetime:
            mock_now = datetime(2025, 1, 20, 10, 0, 0)
            mock_datetime.now.return_value = mock_now
            
            task.start("Starting task...")
            
            assert task.status == TaskStatus.IN_PROGRESS
            assert task.current_activity == "Starting task..."
            assert task.start_time == mock_now
            assert task.end_time is None
    
    def test_task_activity_updates(self):
        """Test Gemini Principle 2: Agent Activity Instrumentation"""
        task = Task(description="Test task")
        task.start("Initial activity")
        
        # Test activity updates
        task.update_activity("Analyzing requirements...")
        assert task.current_activity == "Analyzing requirements..."
        
        task.update_activity("Processing data...")
        assert task.current_activity == "Processing data..."
        
        task.update_activity("Finalizing results...")
        assert task.current_activity == "Finalizing results..."
    
    def test_task_completion(self):
        """Test successful task completion"""
        task = Task(description="Test task")
        task.start("Working...")

        with patch('backend.src.agents.task_models.datetime') as mock_datetime:
            mock_end = datetime(2025, 1, 20, 10, 5, 0)
            mock_datetime.now.return_value = mock_end

            task.complete("Task finished successfully")

            assert task.status == TaskStatus.COMPLETED
            assert task.current_activity == "Task finished successfully"
            assert task.end_time == mock_end
            assert task.error_message is None
    
    def test_task_failure(self):
        """Test task failure handling"""
        task = Task(description="Test task")
        task.start("Working...")
        
        with patch('backend.src.agents.task_models.datetime') as mock_datetime:
            mock_end = datetime(2025, 1, 20, 10, 5, 0)
            mock_datetime.now.return_value = mock_end
            
            task.fail("Something went wrong", "Failed during processing")
            
            assert task.status == TaskStatus.FAILED
            assert task.error_message == "Something went wrong"
            assert task.current_activity == "Failed during processing"
            assert task.end_time == mock_end
    
    def test_task_duration_calculation(self):
        """Test task duration calculation"""
        task = Task(description="Test task")
        
        # No duration when not started
        assert task.get_duration_seconds() is None
        
        # Set times manually for testing
        start_time = datetime(2025, 1, 20, 10, 0, 0)
        end_time = datetime(2025, 1, 20, 10, 5, 30)  # 5.5 minutes
        
        task.start_time = start_time
        task.end_time = end_time
        
        duration = task.get_duration_seconds()
        assert duration == 330.0  # 5.5 minutes = 330 seconds
    
    def test_task_serialization(self):
        """Test task to_dict serialization"""
        task = Task(
            description="Test task",
            metadata={"test_key": "test_value"}
        )
        task.start("Working...")
        task.update_activity("Processing...")
        
        data = task.to_dict()
        
        assert data["description"] == "Test task"
        assert data["status"] == "in_progress"
        assert data["current_activity"] == "Processing..."
        assert data["metadata"]["test_key"] == "test_value"
        assert "id" in data
        assert "start_time" in data


class TestSuggestedAction:
    """Test SuggestedAction for post-execution guidance"""
    
    def test_suggested_action_creation(self):
        """Test basic suggested action creation"""
        action = SuggestedAction(
            action_type=SuggestedActionType.DOWNLOAD_PROJECT,
            title="Download Project",
            description="Download the generated codebase"
        )
        
        assert action.action_type == SuggestedActionType.DOWNLOAD_PROJECT
        assert action.title == "Download Project"
        assert action.description == "Download the generated codebase"
        assert action.enabled is True
        assert action.metadata is None
    
    def test_suggested_action_with_metadata(self):
        """Test suggested action with metadata"""
        action = SuggestedAction(
            action_type=SuggestedActionType.RETRY_PLAN,
            title="Retry Plan",
            description="Attempt to run the plan again",
            enabled=False,
            metadata={"retry_count": 2, "last_error": "timeout"}
        )
        
        assert action.enabled is False
        assert action.metadata["retry_count"] == 2
        assert action.metadata["last_error"] == "timeout"


class TestExecutionResult:
    """Test ExecutionResult with suggested actions"""
    
    def test_successful_execution_result(self):
        """Test successful execution result"""
        completed_tasks = [
            Task(description="Task 1"),
            Task(description="Task 2")
        ]
        
        result = ExecutionResult(
            success=True,
            completed_tasks=completed_tasks,
            total_duration_seconds=45.5
        )
        
        assert result.success is True
        assert len(result.completed_tasks) == 2
        assert result.failed_task is None
        assert result.total_duration_seconds == 45.5
    
    def test_failed_execution_result(self):
        """Test failed execution result"""
        completed_task = Task(description="Completed task")
        failed_task = Task(description="Failed task")
        failed_task.fail("Error occurred")
        
        result = ExecutionResult(
            success=False,
            completed_tasks=[completed_task],
            failed_task=failed_task,
            total_duration_seconds=30.0
        )
        
        assert result.success is False
        assert len(result.completed_tasks) == 1
        assert result.failed_task == failed_task
        assert result.total_duration_seconds == 30.0
    
    def test_success_suggested_actions(self):
        """Test Gemini Principle 3: Success suggested actions"""
        result = ExecutionResult(success=True, completed_tasks=[])
        actions = result.get_success_actions()
        
        assert len(actions) == 3
        action_types = [action.action_type for action in actions]
        
        assert SuggestedActionType.DOWNLOAD_PROJECT in action_types
        assert SuggestedActionType.START_NEW_GOAL in action_types
        assert SuggestedActionType.VIEW_DOCUMENTATION in action_types
    
    def test_failure_suggested_actions_with_retry(self):
        """Test failure suggested actions with retry enabled"""
        result = ExecutionResult(success=False, completed_tasks=[])
        actions = result.get_failure_actions(allow_retry=True)
        
        assert len(actions) == 3
        action_types = [action.action_type for action in actions]
        
        assert SuggestedActionType.RETRY_PLAN in action_types
        assert SuggestedActionType.EDIT_GOAL_REPLAN in action_types
        assert SuggestedActionType.REPORT_ISSUE in action_types
    
    def test_failure_suggested_actions_no_retry(self):
        """Test failure suggested actions with retry disabled"""
        result = ExecutionResult(success=False, completed_tasks=[])
        actions = result.get_failure_actions(allow_retry=False)
        
        assert len(actions) == 2
        action_types = [action.action_type for action in actions]
        
        assert SuggestedActionType.RETRY_PLAN not in action_types
        assert SuggestedActionType.EDIT_GOAL_REPLAN in action_types
        assert SuggestedActionType.REPORT_ISSUE in action_types
    
    def test_execution_result_serialization(self):
        """Test execution result serialization"""
        task = Task(description="Test task")
        action = SuggestedAction(
            action_type=SuggestedActionType.DOWNLOAD_PROJECT,
            title="Download",
            description="Download project"
        )
        
        result = ExecutionResult(
            success=True,
            completed_tasks=[task],
            total_duration_seconds=60.0,
            summary_message="All tasks completed"
        )
        result.add_suggested_action(action)
        
        data = result.to_dict()
        
        assert data["success"] is True
        assert len(data["completed_tasks"]) == 1
        assert data["failed_task"] is None
        assert data["total_duration_seconds"] == 60.0
        assert data["summary_message"] == "All tasks completed"
        assert len(data["suggested_actions"]) == 1
        assert data["suggested_actions"][0]["action_type"] == "download_project"
