"""
Component Discovery Module - Intelligent component search and recommendation engine.

Implements two-stage search architecture:
1. GitHub API search for candidate repositories
2. Health analysis and LLM synthesis for recommendations

Follows <PERSON><PERSON>uilter's Hybrid Intelligence Principle:
- Deterministic foundation: GitHub search and health scoring
- AI enhancement: LLM-powered explanations and recommendations

TODO: REPLACE_MOCK - LLM integration uses real APIs but includes fallback patterns
TODO: IMPLEMENT - Real GitHub API integration with rate limiting and caching
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Any, Optional, Protocol
from enum import Enum
import math

from ..integrations.llm_client import LLMClient
from ..integrations.real_llm_client import create_llm_client
from ..state.patterns import PATTERN_PALETTE


class MaintenanceStatus(Enum):
    """Repository maintenance status based on activity"""
    ACTIVE = "active"          # Recent commits (< 30 days)
    INFREQUENT = "infrequent"  # Some activity (30-180 days)
    DORMANT = "dormant"        # Little activity (> 180 days)


class LicenseCompatibility(Enum):
    """License compatibility levels"""
    COMPATIBLE = "compatible"      # Permissive licenses (MIT, Apache, BSD)
    RESTRICTIVE = "restrictive"    # Copyleft licenses (GPL, AGPL)
    PROPRIETARY = "proprietary"    # Commercial or proprietary
    UNKNOWN = "unknown"           # No license or unrecognized


@dataclass
class CandidateRepo:
    """
    Repository candidate from GitHub search.
    
    Contains all metadata needed for health analysis and recommendation.
    """
    name: str
    full_name: str  # owner/repo
    description: str
    html_url: str
    clone_url: str
    
    # Popularity metrics
    stars: int
    forks: int
    watchers: int
    open_issues: int
    
    # Activity metrics
    created_at: datetime
    updated_at: datetime
    pushed_at: datetime
    
    # Technical details
    language: str
    topics: List[str] = field(default_factory=list)
    license_info: Optional[Dict[str, Any]] = None
    
    # Additional metadata
    size_kb: int = 0
    default_branch: str = "main"
    archived: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "name": self.name,
            "full_name": self.full_name,
            "description": self.description,
            "html_url": self.html_url,
            "clone_url": self.clone_url,
            "stars": self.stars,
            "forks": self.forks,
            "watchers": self.watchers,
            "open_issues": self.open_issues,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "pushed_at": self.pushed_at.isoformat(),
            "language": self.language,
            "topics": self.topics,
            "license_info": self.license_info,
            "size_kb": self.size_kb,
            "default_branch": self.default_branch,
            "archived": self.archived
        }
    
    @classmethod
    def from_github_data(cls, github_repo: Dict[str, Any]) -> "CandidateRepo":
        """Create CandidateRepo from GitHub API response"""
        # Parse datetime strings
        created_at = datetime.fromisoformat(github_repo["created_at"].replace("Z", "+00:00"))
        updated_at = datetime.fromisoformat(github_repo["updated_at"].replace("Z", "+00:00"))
        pushed_at = datetime.fromisoformat(github_repo["pushed_at"].replace("Z", "+00:00"))
        
        return cls(
            name=github_repo["name"],
            full_name=github_repo["full_name"],
            description=github_repo.get("description", ""),
            html_url=github_repo["html_url"],
            clone_url=github_repo["clone_url"],
            stars=github_repo["stargazers_count"],
            forks=github_repo["forks_count"],
            watchers=github_repo["watchers_count"],
            open_issues=github_repo["open_issues_count"],
            created_at=created_at,
            updated_at=updated_at,
            pushed_at=pushed_at,
            language=github_repo.get("language", ""),
            topics=github_repo.get("topics", []),
            license_info=github_repo.get("license"),
            size_kb=github_repo.get("size", 0),
            default_branch=github_repo.get("default_branch", "main"),
            archived=github_repo.get("archived", False)
        )


@dataclass
class HealthReport:
    """
    Quantifiable health assessment for a repository.
    
    Implements transparent scoring algorithm:
    - Activity (40%): Recent commits and maintenance
    - Popularity (30%): Stars, forks, community engagement
    - License (20%): Compatibility with project requirements
    - Security (10%): Known vulnerabilities and issues
    """
    repo_name: str
    health_score: int  # 0-100 overall score
    
    # Score breakdown (transparent algorithm)
    activity_score: float    # 0-40 points
    popularity_score: float  # 0-30 points
    license_score: float     # 0-20 points
    security_score: float    # 0-10 points
    
    # Detailed analysis
    maintenance_status: MaintenanceStatus
    license_compatibility: LicenseCompatibility
    days_since_last_commit: int
    vulnerability_count: int = 0
    vulnerability_details: List[Dict[str, Any]] = field(default_factory=list)
    
    # Recommendations
    risk_factors: List[str] = field(default_factory=list)
    positive_indicators: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "repo_name": self.repo_name,
            "health_score": self.health_score,
            "score_breakdown": {
                "activity": self.activity_score,
                "popularity": self.popularity_score,
                "license": self.license_score,
                "security": self.security_score
            },
            "maintenance_status": self.maintenance_status.value,
            "license_compatibility": self.license_compatibility.value,
            "days_since_last_commit": self.days_since_last_commit,
            "vulnerability_count": self.vulnerability_count,
            "vulnerability_details": self.vulnerability_details,
            "risk_factors": self.risk_factors,
            "positive_indicators": self.positive_indicators
        }


@dataclass
class RecommendedComponent:
    """
    Final recommendation combining repository data, health analysis, and LLM synthesis.
    
    Represents the complete evaluation of a component for user decision-making.
    """
    candidate_repo: CandidateRepo
    health_report: HealthReport
    
    # LLM-generated insights
    llm_explanation: str
    pros_and_cons: Dict[str, List[str]]  # {"pros": [...], "cons": [...]}
    integration_complexity: str  # "Low", "Medium", "High"
    integration_notes: str
    
    # Ranking and comparison
    recommendation_rank: int  # 1-based ranking
    confidence_score: float   # 0.0-1.0
    alternative_to: Optional[str] = None  # Name of component this is alternative to
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "candidate_repo": self.candidate_repo.to_dict(),
            "health_report": self.health_report.to_dict(),
            "llm_explanation": self.llm_explanation,
            "pros_and_cons": self.pros_and_cons,
            "integration_complexity": self.integration_complexity,
            "integration_notes": self.integration_notes,
            "recommendation_rank": self.recommendation_rank,
            "confidence_score": self.confidence_score,
            "alternative_to": self.alternative_to
        }


# Compatible licenses for scoring algorithm
COMPATIBLE_LICENSES = {
    "mit", "apache-2.0", "bsd-2-clause", "bsd-3-clause",
    "isc", "unlicense", "0bsd", "cc0-1.0"
}

RESTRICTIVE_LICENSES = {
    "gpl-2.0", "gpl-3.0", "lgpl-2.1", "lgpl-3.0",
    "agpl-3.0", "mpl-2.0", "epl-2.0"
}


class ComponentMatcher:
    """
    Main orchestrator for component discovery process.

    Implements the complete two-stage search architecture:
    1. GitHub search for candidates
    2. Health analysis and LLM synthesis for recommendations

    Follows Hybrid Intelligence Principle:
    - Deterministic: GitHub search and health scoring
    - AI Enhancement: LLM explanations and recommendations
    """

    def __init__(self, github_client=None, llm_client=None, use_real_apis: bool = True):
        """
        Initialize component matcher with dependency injection.

        Args:
            github_client: GitHub client (injected for testing)
            llm_client: LLM client (injected for testing)
            use_real_apis: Whether to use real APIs or mocks
        """
        # GitHub client dependency injection
        if github_client is None:
            from .github_search import create_github_client
            self.github_client = create_github_client(use_real_api=use_real_apis)
        else:
            self.github_client = github_client

        # LLM client dependency injection
        if llm_client is None:
            try:
                self.llm_client = create_llm_client(use_real_api=use_real_apis)
                print(f"🤖 ComponentMatcher using {'real' if use_real_apis else 'mock'} LLM client")
            except Exception as e:
                print(f"⚠️  Failed to create LLM client: {e}")
                print("🔄 Falling back to mock LLM client")
                self.llm_client = LLMClient()  # Fallback to mock
        else:
            self.llm_client = llm_client

        # Health analyzer
        from .health_analysis import ComponentHealthAnalyzer
        self.health_analyzer = ComponentHealthAnalyzer()

    async def find_best_components(
        self,
        brainstorming_results: Dict[str, Any],
        max_recommendations: int = 5
    ) -> Dict[str, List[RecommendedComponent]]:
        """
        Find and recommend the best components based on brainstorming results.

        Args:
            brainstorming_results: Output from BrainstormingSession.to_dict()
            max_recommendations: Maximum recommendations per pattern

        Returns:
            Dictionary mapping pattern names to recommended components
        """
        print("🔍 Starting component discovery process...")

        # Extract requirements from brainstorming results
        requirements = self._extract_requirements(brainstorming_results)
        print(f"📋 Extracted requirements: {len(requirements)} patterns identified")

        # Find components for each pattern
        all_recommendations = {}

        for pattern_name, pattern_requirements in requirements.items():
            print(f"\n🎯 Searching for {pattern_name} components...")

            try:
                # Stage 1: GitHub search for candidates
                candidates = await self._search_candidates(pattern_name, pattern_requirements)
                print(f"   Found {len(candidates)} candidates")

                if not candidates:
                    print(f"   ⚠️  No candidates found for {pattern_name}")
                    continue

                # Stage 2: Health analysis and LLM synthesis
                recommendations = await self._analyze_and_recommend(
                    candidates, pattern_name, pattern_requirements, max_recommendations
                )
                print(f"   ✅ Generated {len(recommendations)} recommendations")

                all_recommendations[pattern_name] = recommendations

            except Exception as e:
                print(f"   ❌ Error processing {pattern_name}: {e}")
                continue

        print(f"\n🎉 Component discovery complete! Found recommendations for {len(all_recommendations)} patterns")
        return all_recommendations

    def _extract_requirements(self, brainstorming_results: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """Extract component requirements from brainstorming session results"""
        requirements = {}

        # Get high-confidence patterns from brainstorming
        pattern_scores = brainstorming_results.get("pattern_scores", {})
        responses = brainstorming_results.get("responses", [])

        # Create response lookup
        response_map = {r["question_id"]: r["answer"] for r in responses}

        # Extract patterns with confidence >= 0.6
        for pattern_name, pattern_data in pattern_scores.items():
            if pattern_data["confidence"] >= 0.6:
                requirements[pattern_name] = {
                    "confidence": pattern_data["confidence"],
                    "language": response_map.get("developer_preferences", "Python"),
                    "external_apis": response_map.get("external_apis", ""),
                    "scale": response_map.get("expected_scale", ""),
                    "deployment": response_map.get("deployment_target", ""),
                    "communication": response_map.get("communication_style", "")
                }

        return requirements

    async def _search_candidates(
        self,
        pattern_name: str,
        requirements: Dict[str, Any]
    ) -> List[CandidateRepo]:
        """Search for candidate repositories using GitHub API"""
        try:
            # Use GitHub client to search for repositories
            response = await self.github_client.search_repositories_for_pattern(
                pattern_name, requirements, max_results=10
            )

            if not response.success:
                print(f"   ⚠️  GitHub search failed: {response.error_message}")
                return []

            # Convert GitHub data to CandidateRepo objects
            candidates = []
            for repo_data in response.data.get("items", []):
                try:
                    candidate = CandidateRepo.from_github_data(repo_data)
                    candidates.append(candidate)
                except Exception as e:
                    print(f"   ⚠️  Failed to parse repository data: {e}")
                    continue

            return candidates

        except Exception as e:
            print(f"   ❌ Error in candidate search: {e}")
            return []

    async def _analyze_and_recommend(
        self,
        candidates: List[CandidateRepo],
        pattern_name: str,
        requirements: Dict[str, Any],
        max_recommendations: int
    ) -> List[RecommendedComponent]:
        """Analyze candidates and generate LLM-powered recommendations"""
        if not candidates:
            return []

        # Perform health analysis on all candidates
        health_reports = []
        for candidate in candidates:
            try:
                health_report = await self.health_analyzer.analyze_repository_health(candidate)
                health_reports.append((candidate, health_report))
            except Exception as e:
                print(f"   ⚠️  Health analysis failed for {candidate.name}: {e}")
                continue

        if not health_reports:
            return []

        # Sort by health score (best first)
        health_reports.sort(key=lambda x: x[1].health_score, reverse=True)

        # Take top candidates for LLM analysis
        top_candidates = health_reports[:max_recommendations]

        # Generate LLM synthesis for recommendations
        recommendations = []
        for rank, (candidate, health_report) in enumerate(top_candidates, 1):
            try:
                llm_insights = await self._generate_llm_insights(
                    candidate, health_report, pattern_name, requirements, rank
                )

                recommendation = RecommendedComponent(
                    candidate_repo=candidate,
                    health_report=health_report,
                    llm_explanation=llm_insights["explanation"],
                    pros_and_cons=llm_insights["pros_and_cons"],
                    integration_complexity=llm_insights["integration_complexity"],
                    integration_notes=llm_insights["integration_notes"],
                    recommendation_rank=rank,
                    confidence_score=llm_insights["confidence_score"]
                )

                recommendations.append(recommendation)

            except Exception as e:
                print(f"   ⚠️  LLM synthesis failed for {candidate.name}: {e}")
                # Create basic recommendation without LLM insights
                basic_recommendation = RecommendedComponent(
                    candidate_repo=candidate,
                    health_report=health_report,
                    llm_explanation=f"Repository with health score {health_report.health_score}/100",
                    pros_and_cons={"pros": ["Available for analysis"], "cons": ["LLM analysis unavailable"]},
                    integration_complexity="Unknown",
                    integration_notes="Manual evaluation required",
                    recommendation_rank=rank,
                    confidence_score=0.5
                )
                recommendations.append(basic_recommendation)

        return recommendations

    async def _generate_llm_insights(
        self,
        candidate: CandidateRepo,
        health_report: HealthReport,
        pattern_name: str,
        requirements: Dict[str, Any],
        rank: int
    ) -> Dict[str, Any]:
        """Generate LLM-powered insights for a component recommendation"""
        try:
            # Build context for LLM
            context = self._build_llm_context(candidate, health_report, pattern_name, requirements)

            # Create prompt for component analysis
            prompt = f"""Analyze this software component for a {pattern_name} pattern implementation:

{context}

Provide a comprehensive analysis in JSON format:
{{
  "explanation": "Clear explanation of why this component is recommended (2-3 sentences)",
  "pros_and_cons": {{
    "pros": ["List of 3-4 key advantages"],
    "cons": ["List of 2-3 potential drawbacks or considerations"]
  }},
  "integration_complexity": "Low" | "Medium" | "High",
  "integration_notes": "Specific notes about integration effort and requirements",
  "confidence_score": 0.85
}}

Focus on:
1. How well it fits the {pattern_name} pattern
2. Integration effort and complexity
3. Long-term maintenance considerations
4. Community and ecosystem support"""

            # Get LLM response
            response = await self.llm_client.generate_brainstorming_response(
                prompt, {"task": "component_analysis"}
            )

            if response.success:
                return await self._parse_llm_insights(response.data.get("response", ""))
            else:
                print(f"   ⚠️  LLM analysis failed: {response.data.get('error', 'Unknown error')}")
                return self._get_fallback_insights(candidate, health_report, rank)

        except Exception as e:
            print(f"   ⚠️  Error in LLM insights generation: {e}")
            return self._get_fallback_insights(candidate, health_report, rank)

    def _build_llm_context(
        self,
        candidate: CandidateRepo,
        health_report: HealthReport,
        pattern_name: str,
        requirements: Dict[str, Any]
    ) -> str:
        """Build structured context for LLM analysis"""
        context_parts = [
            f"Repository: {candidate.full_name}",
            f"Description: {candidate.description}",
            f"Language: {candidate.language}",
            f"Stars: {candidate.stars:,}",
            f"Forks: {candidate.forks:,}",
            f"Last updated: {candidate.pushed_at.strftime('%Y-%m-%d')}",
            f"Topics: {', '.join(candidate.topics)}",
            "",
            f"Health Score: {health_report.health_score}/100",
            f"Maintenance Status: {health_report.maintenance_status.value}",
            f"License: {health_report.license_compatibility.value}",
            f"Days since last commit: {health_report.days_since_last_commit}",
            "",
            f"Project Requirements:",
            f"- Pattern: {pattern_name}",
            f"- Language preference: {requirements.get('language', 'Any')}",
            f"- Scale: {requirements.get('scale', 'Not specified')}",
            f"- Deployment: {requirements.get('deployment', 'Not specified')}",
            ""
        ]

        if health_report.positive_indicators:
            context_parts.append("Positive Indicators:")
            for indicator in health_report.positive_indicators:
                context_parts.append(f"- {indicator}")
            context_parts.append("")

        if health_report.risk_factors:
            context_parts.append("Risk Factors:")
            for risk in health_report.risk_factors:
                context_parts.append(f"- {risk}")
            context_parts.append("")

        return "\n".join(context_parts)

    async def _parse_llm_insights(self, llm_response: str) -> Dict[str, Any]:
        """Parse LLM response into structured insights"""
        import json
        import re

        try:
            # Extract JSON from response (handle markdown code blocks)
            json_match = re.search(r'```(?:json)?\s*(\{.*?\})\s*```', llm_response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # Try to find JSON without code blocks
                json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)
                else:
                    raise ValueError("No JSON found in LLM response")

            data = json.loads(json_str)

            # Validate required fields and provide defaults
            return {
                "explanation": data.get("explanation", "Component analysis available"),
                "pros_and_cons": data.get("pros_and_cons", {"pros": [], "cons": []}),
                "integration_complexity": data.get("integration_complexity", "Medium"),
                "integration_notes": data.get("integration_notes", "Standard integration expected"),
                "confidence_score": float(data.get("confidence_score", 0.7))
            }

        except Exception as e:
            print(f"   ⚠️  Failed to parse LLM insights: {e}")
            print(f"   LLM Response: {llm_response[:200]}...")
            return self._get_fallback_insights(None, None, 1)

    def _get_fallback_insights(
        self,
        candidate: Optional[CandidateRepo],
        health_report: Optional[HealthReport],
        rank: int
    ) -> Dict[str, Any]:
        """Generate fallback insights when LLM analysis fails"""
        # TODO: REPLACE_MOCK - Fallback insights when LLM unavailable

        if health_report:
            if health_report.health_score >= 80:
                explanation = f"High-quality component with excellent health score ({health_report.health_score}/100)"
                complexity = "Low"
                confidence = 0.8
            elif health_report.health_score >= 60:
                explanation = f"Solid component with good health score ({health_report.health_score}/100)"
                complexity = "Medium"
                confidence = 0.7
            else:
                explanation = f"Component with moderate health score ({health_report.health_score}/100)"
                complexity = "High"
                confidence = 0.5
        else:
            explanation = "Component available for evaluation"
            complexity = "Medium"
            confidence = 0.5

        pros = ["Available for integration"]
        cons = ["Requires manual evaluation"]

        if health_report:
            if health_report.maintenance_status == MaintenanceStatus.ACTIVE:
                pros.append("Actively maintained")
            elif health_report.maintenance_status == MaintenanceStatus.DORMANT:
                cons.append("Limited recent activity")

            if health_report.license_compatibility == LicenseCompatibility.COMPATIBLE:
                pros.append("Permissive license")
            elif health_report.license_compatibility == LicenseCompatibility.RESTRICTIVE:
                cons.append("Restrictive license terms")

        return {
            "explanation": explanation,
            "pros_and_cons": {"pros": pros, "cons": cons},
            "integration_complexity": complexity,
            "integration_notes": "Manual integration assessment recommended",
            "confidence_score": confidence
        }


# Factory function for creating component matcher
def create_component_matcher(use_real_apis: bool = True) -> ComponentMatcher:
    """
    Factory function for creating ComponentMatcher with appropriate clients.

    Args:
        use_real_apis: Whether to use real APIs or mock clients

    Returns:
        ComponentMatcher instance with injected dependencies
    """
    return ComponentMatcher(use_real_apis=use_real_apis)
