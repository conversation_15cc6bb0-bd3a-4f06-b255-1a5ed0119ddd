"""
GitHub Search Integration - Two-stage component discovery system.

Implements dependency injection pattern for GitHub API clients:
- BaseGitHubClient: Protocol defining the interface
- MockGitHubClient: Predictable test data
- RealGitHubClient: Actual GitHub API integration
- Factory pattern for environment-based selection

TODO: IMPLEMENT - Real GitHub API integration with PyGithub
TODO: ENHANCE - Add caching layer for API responses
TODO: IMPLEMENT - Rate limiting and retry logic
"""

import asyncio
import os
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Protocol
from datetime import datetime, timedelta
import random

from .component_discovery import CandidateRepo
from ..integrations.base_client import APIResponse


class BaseGitHubClient(Protocol):
    """
    Protocol defining the GitHub client interface for dependency injection.
    
    This allows us to swap between mock and real implementations seamlessly.
    """
    
    async def search_repositories_for_pattern(
        self, 
        pattern_name: str, 
        requirements: Dict[str, Any],
        max_results: int = 10
    ) -> APIResponse:
        """Search for repositories matching a specific architectural pattern"""
        ...
    
    async def get_repository_details(self, full_name: str) -> APIResponse:
        """Get detailed information about a specific repository"""
        ...
    
    async def get_license_info(self, full_name: str) -> APIResponse:
        """Get license information for a repository"""
        ...
    
    async def test_connection(self) -> APIResponse:
        """Test if the GitHub API connection is working"""
        ...


class MockGitHubClient:
    """
    Mock GitHub client for testing and development.
    
    Returns predictable, static data that covers various scenarios:
    - High-quality repositories with good health scores
    - Medium-quality repositories with some issues
    - Low-quality repositories for testing edge cases
    
    TODO: REPLACE_MOCK - Used only for testing, not production
    """
    
    def __init__(self):
        self._mock_repositories = self._generate_mock_repositories()
    
    async def search_repositories_for_pattern(
        self, 
        pattern_name: str, 
        requirements: Dict[str, Any],
        max_results: int = 10
    ) -> APIResponse:
        """Mock repository search with pattern-specific results"""
        await asyncio.sleep(0.1)  # Simulate API delay
        
        # Filter repositories based on pattern
        pattern_repos = self._get_repos_for_pattern(pattern_name)
        
        # Apply language filter if specified
        language = requirements.get("language", "").lower()
        if language and "let codequilter decide" not in language:
            pattern_repos = [
                repo for repo in pattern_repos
                if repo.get("language", "").lower() == language
            ]
        
        # Limit results
        results = pattern_repos[:max_results]
        
        return APIResponse(
            success=True,
            data={
                "total_count": len(pattern_repos),
                "items": results
            }
        )
    
    async def get_repository_details(self, full_name: str) -> APIResponse:
        """Mock repository details"""
        await asyncio.sleep(0.05)
        
        # Find repository in mock data
        for repo in self._mock_repositories:
            if repo["full_name"] == full_name:
                return APIResponse(success=True, data=repo)
        
        return APIResponse(
            success=False, 
            error_message=f"Repository {full_name} not found",
            status_code=404
        )
    
    async def get_license_info(self, full_name: str) -> APIResponse:
        """Mock license information"""
        await asyncio.sleep(0.05)
        
        # Find repository and return license info
        for repo in self._mock_repositories:
            if repo["full_name"] == full_name:
                license_info = repo.get("license")
                if license_info:
                    return APIResponse(success=True, data={"license": license_info})
                else:
                    return APIResponse(
                        success=False,
                        error_message="No license found",
                        status_code=404
                    )
        
        return APIResponse(
            success=False,
            error_message=f"Repository {full_name} not found",
            status_code=404
        )
    
    async def test_connection(self) -> APIResponse:
        """Mock connection test"""
        await asyncio.sleep(0.1)
        return APIResponse(
            success=True,
            data={"status": "connected", "client_type": "mock"}
        )
    
    def _get_repos_for_pattern(self, pattern_name: str) -> List[Dict[str, Any]]:
        """Get mock repositories relevant to a specific pattern"""
        pattern_keywords = {
            "rest_api": ["api", "rest", "fastapi", "flask", "express"],
            "api_gateway": ["gateway", "proxy", "router", "middleware"],
            "message_queue": ["queue", "celery", "redis", "rabbitmq", "kafka"],
            "pub_sub": ["pubsub", "events", "messaging", "notifications"],
            "adapter": ["adapter", "connector", "integration", "wrapper"]
        }
        
        keywords = pattern_keywords.get(pattern_name, [])
        if not keywords:
            return self._mock_repositories[:5]  # Return some default repos
        
        matching_repos = []
        for repo in self._mock_repositories:
            repo_text = f"{repo['name']} {repo['description']} {' '.join(repo.get('topics', []))}".lower()
            if any(keyword in repo_text for keyword in keywords):
                matching_repos.append(repo)
        
        return matching_repos
    
    def _generate_mock_repositories(self) -> List[Dict[str, Any]]:
        """Generate comprehensive mock repository data for testing"""
        # TODO: REPLACE_MOCK - Remove when real API is implemented
        
        base_date = datetime.now()
        
        return [
            # High-quality REST API framework
            {
                "name": "fastapi-advanced",
                "full_name": "awesome-org/fastapi-advanced",
                "description": "Production-ready FastAPI framework with advanced features",
                "html_url": "https://github.com/awesome-org/fastapi-advanced",
                "clone_url": "https://github.com/awesome-org/fastapi-advanced.git",
                "stargazers_count": 15000,
                "forks_count": 1200,
                "watchers_count": 15000,
                "open_issues_count": 25,
                "language": "Python",
                "topics": ["fastapi", "api", "rest", "python", "web-framework"],
                "created_at": (base_date - timedelta(days=800)).isoformat() + "Z",
                "updated_at": (base_date - timedelta(days=2)).isoformat() + "Z",
                "pushed_at": (base_date - timedelta(days=2)).isoformat() + "Z",
                "license": {"key": "mit", "name": "MIT License"},
                "size": 2500,
                "default_branch": "main",
                "archived": False
            },
            
            # Medium-quality API Gateway
            {
                "name": "api-gateway-lite",
                "full_name": "community/api-gateway-lite",
                "description": "Lightweight API gateway for microservices",
                "html_url": "https://github.com/community/api-gateway-lite",
                "clone_url": "https://github.com/community/api-gateway-lite.git",
                "stargazers_count": 3500,
                "forks_count": 280,
                "watchers_count": 3500,
                "open_issues_count": 45,
                "language": "Python",
                "topics": ["gateway", "api", "microservices", "proxy"],
                "created_at": (base_date - timedelta(days=600)).isoformat() + "Z",
                "updated_at": (base_date - timedelta(days=15)).isoformat() + "Z",
                "pushed_at": (base_date - timedelta(days=15)).isoformat() + "Z",
                "license": {"key": "apache-2.0", "name": "Apache License 2.0"},
                "size": 1800,
                "default_branch": "main",
                "archived": False
            },
            
            # High-quality message queue
            {
                "name": "redis-queue-pro",
                "full_name": "enterprise/redis-queue-pro",
                "description": "Enterprise-grade Redis-based task queue system",
                "html_url": "https://github.com/enterprise/redis-queue-pro",
                "clone_url": "https://github.com/enterprise/redis-queue-pro.git",
                "stargazers_count": 8500,
                "forks_count": 650,
                "watchers_count": 8500,
                "open_issues_count": 12,
                "language": "Python",
                "topics": ["redis", "queue", "tasks", "celery", "workers"],
                "created_at": (base_date - timedelta(days=900)).isoformat() + "Z",
                "updated_at": (base_date - timedelta(days=5)).isoformat() + "Z",
                "pushed_at": (base_date - timedelta(days=5)).isoformat() + "Z",
                "license": {"key": "bsd-3-clause", "name": "BSD 3-Clause License"},
                "size": 3200,
                "default_branch": "main",
                "archived": False
            },
            
            # Lower-quality but functional adapter
            {
                "name": "simple-adapter",
                "full_name": "individual/simple-adapter",
                "description": "Basic adapter pattern implementation",
                "html_url": "https://github.com/individual/simple-adapter",
                "clone_url": "https://github.com/individual/simple-adapter.git",
                "stargazers_count": 450,
                "forks_count": 35,
                "watchers_count": 450,
                "open_issues_count": 8,
                "language": "Python",
                "topics": ["adapter", "pattern", "integration"],
                "created_at": (base_date - timedelta(days=400)).isoformat() + "Z",
                "updated_at": (base_date - timedelta(days=60)).isoformat() + "Z",
                "pushed_at": (base_date - timedelta(days=60)).isoformat() + "Z",
                "license": {"key": "mit", "name": "MIT License"},
                "size": 150,
                "default_branch": "master",
                "archived": False
            },
            
            # Dormant repository (for testing edge cases)
            {
                "name": "old-pubsub",
                "full_name": "legacy/old-pubsub",
                "description": "Legacy publish-subscribe system",
                "html_url": "https://github.com/legacy/old-pubsub",
                "clone_url": "https://github.com/legacy/old-pubsub.git",
                "stargazers_count": 1200,
                "forks_count": 180,
                "watchers_count": 1200,
                "open_issues_count": 65,
                "language": "Python",
                "topics": ["pubsub", "messaging", "events"],
                "created_at": (base_date - timedelta(days=1200)).isoformat() + "Z",
                "updated_at": (base_date - timedelta(days=300)).isoformat() + "Z",
                "pushed_at": (base_date - timedelta(days=300)).isoformat() + "Z",
                "license": {"key": "gpl-3.0", "name": "GNU General Public License v3.0"},
                "size": 800,
                "default_branch": "master",
                "archived": False
            }
        ]


class RealGitHubClient:
    """
    Real GitHub API client using PyGithub.
    
    Implements rate limiting, error handling, and caching for production use.
    
    TODO: IMPLEMENT - Complete GitHub API integration
    TODO: ENHANCE - Add intelligent caching layer
    TODO: IMPLEMENT - Advanced search query construction
    """
    
    def __init__(self, api_token: Optional[str] = None):
        self.api_token = api_token or os.getenv("GITHUB_TOKEN")
        # TODO: IMPLEMENT - Initialize PyGithub client
        # self.github = Github(self.api_token) if self.api_token else Github()
        
    async def search_repositories_for_pattern(
        self, 
        pattern_name: str, 
        requirements: Dict[str, Any],
        max_results: int = 10
    ) -> APIResponse:
        """Real GitHub repository search"""
        # TODO: IMPLEMENT - Real GitHub search with advanced queries
        # For now, return error to indicate not implemented
        return APIResponse(
            success=False,
            error_message="Real GitHub API integration not yet implemented",
            status_code=501
        )
    
    async def get_repository_details(self, full_name: str) -> APIResponse:
        """Real repository details from GitHub API"""
        # TODO: IMPLEMENT - Real repository details
        return APIResponse(
            success=False,
            error_message="Real GitHub API integration not yet implemented",
            status_code=501
        )
    
    async def get_license_info(self, full_name: str) -> APIResponse:
        """Real license information from GitHub API"""
        # TODO: IMPLEMENT - Real license information
        return APIResponse(
            success=False,
            error_message="Real GitHub API integration not yet implemented",
            status_code=501
        )
    
    async def test_connection(self) -> APIResponse:
        """Test real GitHub API connection"""
        # TODO: IMPLEMENT - Real connection test
        return APIResponse(
            success=False,
            error_message="Real GitHub API integration not yet implemented",
            status_code=501
        )


def create_github_client(use_real_api: bool = False) -> BaseGitHubClient:
    """
    Factory function for creating GitHub clients based on environment.
    
    Args:
        use_real_api: If True, creates RealGitHubClient. If False, creates MockGitHubClient.
    
    Returns:
        GitHub client implementing BaseGitHubClient protocol
    """
    if use_real_api and os.getenv("GITHUB_TOKEN"):
        print("🔗 Creating real GitHub API client")
        return RealGitHubClient()
    else:
        if use_real_api:
            print("⚠️  GITHUB_TOKEN not found, falling back to mock client")
        else:
            print("🧪 Creating mock GitHub client for testing")
        return MockGitHubClient()
