"""
Brainstorming API endpoints for CodeQuilter.

Provides REST API for the intelligent brainstorming conversation engine,
including session management, question answering, and structured brief generation.
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional
from datetime import datetime

from ..modules.brainstorming import (
    BrainstormingEngine,
    BrainstormingSession,
    Question,
    QuestionnaireResponse,
    get_question_by_id,
    get_all_questions
)
from ..integrations.llm_client import llm_client
from ..integrations.github_client import github_client
from ..session_manager import get_session_manager

router = APIRouter(prefix="/api/projects", tags=["brainstorming"])

# Global brainstorming engine instance with real LLM integration
# TODO: REPLACE_MOCK - Currently uses real LLM APIs, fallback to mock if APIs unavailable
brainstorming_engine = BrainstormingEngine(use_real_llm=True)

# In-memory storage for brainstorming sessions (TODO: Replace with persistent storage)
brainstorming_sessions: Dict[str, BrainstormingSession] = {}


# Request/Response Models
class StartBrainstormingRequest(BaseModel):
    """Request to start a brainstorming session"""
    initial_description: str = Field(
        default="",
        description="Optional initial project description"
    )


class AnswerRequest(BaseModel):
    """Request to submit an answer to a question"""
    question_id: str = Field(description="ID of the question being answered")
    answer: str = Field(description="User's answer to the question")
    confidence: float = Field(
        default=1.0,
        ge=0.0,
        le=1.0,
        description="User's confidence in their answer (0.0-1.0)"
    )


class QuestionResponse(BaseModel):
    """Response containing a question"""
    id: str
    text: str
    question_type: str
    category: str
    options: List[str] = []
    required: bool = True
    help_text: str = ""


class BrainstormingStatusResponse(BaseModel):
    """Response containing brainstorming session status"""
    session_id: str
    progress: float
    responses_count: int
    patterns_identified: int
    decisions_made: int
    conversation_complete: bool
    next_questions: List[QuestionResponse]
    last_updated: str


class PatternConfidenceResponse(BaseModel):
    """Response containing pattern confidence information"""
    pattern_name: str
    confidence: float
    reasoning: str
    contributing_factors: List[str]


class IntelligentDecisionResponse(BaseModel):
    """Response containing an intelligent decision"""
    decision_type: str
    recommendation: str
    confidence: float
    reasoning: str
    alternatives: List[Dict[str, Any]]
    health_metrics: Dict[str, Any]


class StructuredBriefResponse(BaseModel):
    """Response containing the final structured brief"""
    project_overview: Dict[str, Any]
    recommended_patterns: List[Dict[str, Any]]
    intelligent_decisions: List[IntelligentDecisionResponse]
    user_responses: Dict[str, List[Dict[str, Any]]]
    next_steps: List[str]


# Helper Functions
def _get_brainstorming_session(session_id: str) -> BrainstormingSession:
    """Get brainstorming session or raise 404"""
    if session_id not in brainstorming_sessions:
        raise HTTPException(
            status_code=404,
            detail=f"Brainstorming session {session_id} not found"
        )
    return brainstorming_sessions[session_id]


def _question_to_response(question: Question) -> QuestionResponse:
    """Convert Question to QuestionResponse"""
    return QuestionResponse(
        id=question.id,
        text=question.text,
        question_type=question.question_type.value,
        category=question.category.value,
        options=question.options,
        required=question.required,
        help_text=question.help_text
    )


# API Endpoints
@router.post("/{session_id}/brainstorm/start")
async def start_brainstorming(
    session_id: str,
    request: StartBrainstormingRequest
) -> Dict[str, Any]:
    """Initialize brainstorming session with initial user input"""
    # Check if session already exists
    if session_id in brainstorming_sessions:
        raise HTTPException(
            status_code=409,
            detail=f"Brainstorming session {session_id} already exists"
        )

    try:
        # Start new brainstorming session
        session = await brainstorming_engine.start_brainstorming(
            session_id=session_id,
            initial_description=request.initial_description
        )

        # Store session
        brainstorming_sessions[session_id] = session

        # Get next questions
        next_questions = []
        for question_id in session.next_questions:
            question = get_question_by_id(question_id)
            if question:
                next_questions.append(_question_to_response(question))

        return {
            "session_id": session_id,
            "message": "Brainstorming session started successfully",
            "project_description": session.project_description,
            "next_questions": next_questions,
            "created_at": session.created_at.isoformat()
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start brainstorming session: {str(e)}"
        )


@router.post("/{session_id}/brainstorm/answer")
async def submit_answer(
    session_id: str,
    request: AnswerRequest
) -> Dict[str, Any]:
    """Submit answer and get next questions"""
    # Get existing session
    session = _get_brainstorming_session(session_id)

    try:
        # Process the answer
        updated_session = await brainstorming_engine.process_answer(
            session=session,
            question_id=request.question_id,
            answer=request.answer,
            confidence=request.confidence
        )
        
        # Update stored session
        brainstorming_sessions[session_id] = updated_session
        
        # Get next questions
        next_questions = []
        for question_id in updated_session.next_questions:
            question = get_question_by_id(question_id)
            if question:
                next_questions.append(_question_to_response(question))
        
        # Get pattern confidences
        pattern_confidences = [
            PatternConfidenceResponse(
                pattern_name=name,
                confidence=pattern_conf.confidence,
                reasoning=pattern_conf.reasoning,
                contributing_factors=pattern_conf.contributing_factors
            )
            for name, pattern_conf in updated_session.pattern_scores.items()
            if pattern_conf.confidence > 0.3  # Only show patterns with some confidence
        ]
        
        # Get intelligent decisions
        intelligent_decisions = [
            IntelligentDecisionResponse(
                decision_type=decision.decision_type,
                recommendation=decision.recommendation,
                confidence=decision.confidence,
                reasoning=decision.reasoning,
                alternatives=decision.alternatives,
                health_metrics=decision.health_metrics
            )
            for decision in updated_session.intelligent_decisions
        ]
        
        return {
            "session_id": session_id,
            "answer_processed": True,
            "conversation_complete": updated_session.conversation_complete,
            "next_questions": next_questions,
            "pattern_confidences": pattern_confidences,
            "intelligent_decisions": intelligent_decisions,
            "responses_count": len(updated_session.responses),
            "last_updated": updated_session.last_updated.isoformat()
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to process answer: {str(e)}"
        )


@router.get("/{session_id}/brainstorm/status")
async def get_brainstorming_status(session_id: str) -> BrainstormingStatusResponse:
    """Get current brainstorming session status and progress"""
    # Get existing session
    session = _get_brainstorming_session(session_id)

    try:
        # Get session status
        status = await brainstorming_engine.get_session_status(session)
        
        # Get next questions
        next_questions = []
        for question_id in session.next_questions:
            question = get_question_by_id(question_id)
            if question:
                next_questions.append(_question_to_response(question))
        
        return BrainstormingStatusResponse(
            session_id=session_id,
            progress=status["progress"],
            responses_count=status["responses_count"],
            patterns_identified=status["patterns_identified"],
            decisions_made=status["decisions_made"],
            conversation_complete=status["conversation_complete"],
            next_questions=next_questions,
            last_updated=status["last_updated"]
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get session status: {str(e)}"
        )


@router.post("/{session_id}/brainstorm/complete")
async def complete_brainstorming(session_id: str) -> StructuredBriefResponse:
    """Finalize brainstorming and generate structured brief"""
    try:
        # Get existing session
        session = _get_brainstorming_session(session_id)
        
        # Force completion if not already complete
        if not session.conversation_complete:
            session.structured_brief = await brainstorming_engine._generate_structured_brief(session)
            session.conversation_complete = True
            brainstorming_sessions[session_id] = session
        
        # Convert to response format
        intelligent_decisions = [
            IntelligentDecisionResponse(
                decision_type=decision["decision_type"],
                recommendation=decision["recommendation"],
                confidence=decision["confidence"],
                reasoning=decision["reasoning"],
                alternatives=decision["alternatives"],
                health_metrics=decision["health_metrics"]
            )
            for decision in session.structured_brief.get("intelligent_decisions", [])
        ]
        
        return StructuredBriefResponse(
            project_overview=session.structured_brief.get("project_overview", {}),
            recommended_patterns=session.structured_brief.get("recommended_patterns", []),
            intelligent_decisions=intelligent_decisions,
            user_responses=session.structured_brief.get("user_responses", {}),
            next_steps=session.structured_brief.get("next_steps", [])
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to complete brainstorming: {str(e)}"
        )


@router.get("/{session_id}/brainstorm/questions")
async def get_available_questions(session_id: str) -> List[QuestionResponse]:
    """Get all available questions for reference"""
    try:
        questions = get_all_questions()
        return [_question_to_response(question) for question in questions]
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get questions: {str(e)}"
        )


@router.delete("/{session_id}/brainstorm")
async def delete_brainstorming_session(session_id: str) -> Dict[str, str]:
    """Delete a brainstorming session"""
    try:
        if session_id not in brainstorming_sessions:
            raise HTTPException(
                status_code=404,
                detail=f"Brainstorming session {session_id} not found"
            )
        
        del brainstorming_sessions[session_id]
        
        return {
            "message": f"Brainstorming session {session_id} deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete session: {str(e)}"
        )
