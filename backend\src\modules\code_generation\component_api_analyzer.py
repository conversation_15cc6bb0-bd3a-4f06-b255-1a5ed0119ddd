"""
ComponentAPIAnalyzer - Tree-Sitter based component interface analysis.

Extracts public methods, classes, exports, and configuration patterns from
selected components to enable precise integration and adapter generation.

This implements the component-aware code generation capability that allows
Code<PERSON>uilter to understand how components can be integrated together.
"""

import re
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import logging

from ...state.components import ComponentAPI, GitHubRepo
from ...integrations.github_client import GitHubClient
from .api_extraction_utils import Python<PERSON>IExtractor, JavaScriptAPIExtractor

logger = logging.getLogger(__name__)


class ComponentAPIAnalyzer:
    """
    Analyzes component codebases to extract API surface information.
    
    Uses Tree-Sitter parsing for precise code analysis and pattern detection
    to understand how components can be integrated together.
    """
    
    def __init__(self, github_client: Optional[GitHubClient] = None):
        """Initialize the ComponentAPIAnalyzer"""
        # TODO: REPLACE_MOCK - GitHub client integration for fetching component code
        if github_client is None:
            from ...integrations.github_client import GitHubClient
            self.github_client = GitHubClient()  # Mock client for now
        else:
            self.github_client = github_client
            
        # TODO: REPLACE_MOCK - Tree-Sitter integration for real code parsing
        # For v1, we'll use pattern-based analysis as a foundation
        self.supported_languages = ["python", "javascript", "typescript", "java"]
        
    async def analyze_component_api(self, component: GitHubRepo) -> ComponentAPI:
        """
        Analyze a component's API surface and return structured information.
        
        Args:
            component: GitHubRepo object representing the component to analyze
            
        Returns:
            ComponentAPI object with extracted interface information
        """
        logger.info(f"Analyzing API for component: {component.name}")
        
        try:
            # Create ComponentAPI object
            api = ComponentAPI(
                component_name=component.name,
                language=component.get_primary_language().lower(),
                analysis_timestamp=datetime.now()
            )
            
            # Get component source code
            source_files = await self._fetch_component_source(component)
            
            # Analyze based on primary language
            if api.language == "python":
                await self._analyze_python_api(api, source_files)
            elif api.language in ["javascript", "typescript"]:
                await self._analyze_javascript_api(api, source_files)
            elif api.language == "java":
                await self._analyze_java_api(api, source_files)
            else:
                # Generic analysis for unsupported languages
                await self._analyze_generic_api(api, source_files)
                
            logger.info(f"API analysis complete for {component.name}: "
                       f"{len(api.public_methods)} methods, {len(api.exported_classes)} classes")
            
            return api
            
        except Exception as e:
            logger.error(f"Error analyzing component API for {component.name}: {e}")
            # Return minimal API object on error
            return ComponentAPI(
                component_name=component.name,
                language=component.get_primary_language().lower(),
                analysis_timestamp=datetime.now()
            )
    
    async def _fetch_component_source(self, component: GitHubRepo) -> Dict[str, str]:
        """
        Fetch source code files from the component repository.
        
        Returns:
            Dictionary mapping file paths to file contents
        """
        # TODO: REPLACE_MOCK - Real GitHub API integration to fetch source files
        # For now, return mock source code based on component patterns
        
        mock_files = {}
        language = component.get_primary_language().lower()
        
        if language == "python":
            mock_files = self._generate_mock_python_files(component)
        elif language in ["javascript", "typescript"]:
            mock_files = self._generate_mock_javascript_files(component)
        elif language == "java":
            mock_files = self._generate_mock_java_files(component)
            
        logger.info(f"Fetched {len(mock_files)} source files for {component.name}")
        return mock_files
    
    def _generate_mock_python_files(self, component: GitHubRepo) -> Dict[str, str]:
        """Generate mock Python source files based on component pattern"""
        files = {}
        
        # Main module file
        if "fastapi" in component.name.lower() or "api" in component.pattern_match.lower():
            files["main.py"] = '''
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel

app = FastAPI()

class UserModel(BaseModel):
    id: int
    name: str
    email: str

@app.get("/")
async def root():
    return {"message": "Hello World"}

@app.get("/users/{user_id}")
async def get_user(user_id: int):
    return UserModel(id=user_id, name="John Doe", email="<EMAIL>")

@app.post("/users")
async def create_user(user: UserModel):
    return user
'''
        
        elif "auth" in component.name.lower():
            files["auth.py"] = '''
import jwt
from datetime import datetime, timedelta
from typing import Optional

class AuthManager:
    def __init__(self, secret_key: str):
        self.secret_key = secret_key
    
    def create_token(self, user_id: str, expires_in: int = 3600) -> str:
        payload = {
            "user_id": user_id,
            "exp": datetime.utcnow() + timedelta(seconds=expires_in)
        }
        return jwt.encode(payload, self.secret_key, algorithm="HS256")
    
    def verify_token(self, token: str) -> Optional[dict]:
        try:
            return jwt.decode(token, self.secret_key, algorithms=["HS256"])
        except jwt.ExpiredSignatureError:
            return None
'''
        
        return files
    
    def _generate_mock_javascript_files(self, component: GitHubRepo) -> Dict[str, str]:
        """Generate mock JavaScript/TypeScript source files"""
        files = {}
        
        if "express" in component.name.lower() or "api" in component.pattern_match.lower():
            files["index.js"] = '''
const express = require('express');
const app = express();

app.use(express.json());

app.get('/', (req, res) => {
    res.json({ message: 'Hello World' });
});

app.get('/users/:id', (req, res) => {
    const userId = req.params.id;
    res.json({ id: userId, name: 'John Doe', email: '<EMAIL>' });
});

app.post('/users', (req, res) => {
    const user = req.body;
    res.json(user);
});

module.exports = app;
'''
        
        return files
    
    def _generate_mock_java_files(self, component: GitHubRepo) -> Dict[str, str]:
        """Generate mock Java source files"""
        files = {}
        
        if "spring" in component.name.lower():
            files["UserController.java"] = '''
@RestController
@RequestMapping("/api/users")
public class UserController {
    
    @GetMapping("/{id}")
    public ResponseEntity<User> getUser(@PathVariable Long id) {
        User user = new User(id, "John Doe", "<EMAIL>");
        return ResponseEntity.ok(user);
    }
    
    @PostMapping
    public ResponseEntity<User> createUser(@RequestBody User user) {
        return ResponseEntity.ok(user);
    }
}
'''
        
        return files

    async def _analyze_python_api(self, api: ComponentAPI, source_files: Dict[str, str]) -> None:
        """Analyze Python source files to extract API information"""
        for file_path, content in source_files.items():
            # Extract classes
            classes = self._extract_python_classes(content)
            api.exported_classes.extend(classes)

            # Extract functions
            functions = self._extract_python_functions(content)
            api.exported_functions.extend(functions)

            # Extract imports
            imports = self._extract_python_imports(content)
            api.imports.extend(imports)

            # Extract configuration patterns
            config = self._extract_python_config(content)
            api.config_options.update(config)

        # Determine entry points and patterns
        api.entry_points = self._identify_python_entry_points(source_files)
        api.api_patterns = self._identify_python_patterns(source_files)

    async def _analyze_javascript_api(self, api: ComponentAPI, source_files: Dict[str, str]) -> None:
        """Analyze JavaScript/TypeScript source files to extract API information"""
        for file_path, content in source_files.items():
            # Extract exports
            exports = self._extract_javascript_exports(content)
            api.exports.extend(exports)

            # Extract functions
            functions = self._extract_javascript_functions(content)
            api.exported_functions.extend(functions)

            # Extract imports/requires
            imports = self._extract_javascript_imports(content)
            api.imports.extend(imports)

        # Determine entry points and patterns
        api.entry_points = self._identify_javascript_entry_points(source_files)
        api.api_patterns = self._identify_javascript_patterns(source_files)

    async def _analyze_java_api(self, api: ComponentAPI, source_files: Dict[str, str]) -> None:
        """Analyze Java source files to extract API information"""
        for file_path, content in source_files.items():
            # Extract classes
            classes = self._extract_java_classes(content)
            api.exported_classes.extend(classes)

            # Extract methods
            methods = self._extract_java_methods(content)
            api.public_methods.extend(methods)

            # Extract imports
            imports = self._extract_java_imports(content)
            api.imports.extend(imports)

        # Determine entry points and patterns
        api.entry_points = self._identify_java_entry_points(source_files)
        api.api_patterns = self._identify_java_patterns(source_files)

    async def _analyze_generic_api(self, api: ComponentAPI, source_files: Dict[str, str]) -> None:
        """Generic analysis for unsupported languages"""
        # Basic pattern detection
        for file_path, content in source_files.items():
            # Count approximate functions/methods
            function_count = len(re.findall(r'function\s+\w+|def\s+\w+|public\s+\w+', content))
            if function_count > 0:
                api.exported_functions.append({
                    "name": f"detected_functions_in_{file_path}",
                    "count": function_count,
                    "file": file_path
                })

        api.api_patterns = ["generic"]

    # ============================================================================
    # Python API Extraction Methods
    # ============================================================================

    def _extract_python_classes(self, content: str) -> List[Dict[str, Any]]:
        """Extract Python class definitions"""
        return PythonAPIExtractor.extract_classes(content)

    def _extract_python_functions(self, content: str) -> List[Dict[str, Any]]:
        """Extract Python function definitions"""
        return PythonAPIExtractor.extract_functions(content)

    def _extract_python_imports(self, content: str) -> List[Dict[str, str]]:
        """Extract Python import statements"""
        return PythonAPIExtractor.extract_imports(content)

    def _extract_python_config(self, content: str) -> Dict[str, Any]:
        """Extract Python configuration patterns"""
        return PythonAPIExtractor.extract_config(content)

    def _identify_python_entry_points(self, source_files: Dict[str, str]) -> List[str]:
        """Identify Python entry points"""
        return PythonAPIExtractor.identify_entry_points(source_files)

    def _identify_python_patterns(self, source_files: Dict[str, str]) -> List[str]:
        """Identify Python architectural patterns"""
        return PythonAPIExtractor.identify_patterns(source_files)

    # ============================================================================
    # JavaScript API Extraction Methods
    # ============================================================================

    def _extract_javascript_exports(self, content: str) -> List[Dict[str, str]]:
        """Extract JavaScript export statements"""
        return JavaScriptAPIExtractor.extract_exports(content)

    def _extract_javascript_functions(self, content: str) -> List[Dict[str, Any]]:
        """Extract JavaScript function definitions"""
        return JavaScriptAPIExtractor.extract_functions(content)

    def _extract_javascript_imports(self, content: str) -> List[Dict[str, str]]:
        """Extract JavaScript import/require statements"""
        return JavaScriptAPIExtractor.extract_imports(content)

    def _identify_javascript_entry_points(self, source_files: Dict[str, str]) -> List[str]:
        """Identify JavaScript entry points"""
        return JavaScriptAPIExtractor.identify_entry_points(source_files)

    def _identify_javascript_patterns(self, source_files: Dict[str, str]) -> List[str]:
        """Identify JavaScript architectural patterns"""
        return JavaScriptAPIExtractor.identify_patterns(source_files)

    # ============================================================================
    # Java API Extraction Methods (Basic Implementation)
    # ============================================================================

    def _extract_java_classes(self, content: str) -> List[Dict[str, Any]]:
        """Extract Java class definitions"""
        classes = []
        # Basic pattern matching for Java classes
        class_pattern = r'public\s+class\s+(\w+)'
        matches = re.finditer(class_pattern, content)

        for match in matches:
            classes.append({
                "name": match.group(1),
                "visibility": "public",
                "type": "class"
            })

        return classes

    def _extract_java_methods(self, content: str) -> List[Dict[str, Any]]:
        """Extract Java method definitions"""
        methods = []
        # Basic pattern matching for public methods
        method_pattern = r'public\s+\w+\s+(\w+)\s*\([^)]*\)'
        matches = re.finditer(method_pattern, content)

        for match in matches:
            methods.append({
                "name": match.group(1),
                "visibility": "public",
                "signature": match.group(0)
            })

        return methods

    def _extract_java_imports(self, content: str) -> List[Dict[str, str]]:
        """Extract Java import statements"""
        imports = []
        import_pattern = r'import\s+([\w.]+);'
        matches = re.finditer(import_pattern, content)

        for match in matches:
            imports.append({
                "type": "import",
                "package": match.group(1)
            })

        return imports

    def _identify_java_entry_points(self, source_files: Dict[str, str]) -> List[str]:
        """Identify Java entry points"""
        entry_points = []

        for file_path, content in source_files.items():
            # Look for main method
            if 'public static void main' in content:
                entry_points.append(file_path)

            # Look for Spring Boot annotations
            if '@SpringBootApplication' in content:
                entry_points.append(f"{file_path}:SpringBootApp")

        return entry_points

    def _identify_java_patterns(self, source_files: Dict[str, str]) -> List[str]:
        """Identify Java architectural patterns"""
        patterns = []
        all_content = ' '.join(source_files.values())

        if '@RestController' in all_content:
            patterns.append('spring_rest')
        if '@Service' in all_content:
            patterns.append('spring_service')
        if '@Repository' in all_content:
            patterns.append('spring_data')

        return patterns
