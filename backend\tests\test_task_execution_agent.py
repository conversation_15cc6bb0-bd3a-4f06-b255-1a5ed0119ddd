"""
Tests for TaskExecutionAgent.

Tests the core orchestrator with Plan-Do-Verify loop and Gemini's principles:
1. Execution Rationale - plan() returns reasoning
2. Agent Activity Instrumentation - Real-time activity updates  
3. Post-Execution User Actions - Suggested next steps
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock

from backend.src.agents.task_execution_agent import TaskExecutionAgent
from backend.src.agents.task_models import Task, TaskStatus, ExecutionResult
from backend.src.state.project_state import ProjectState, ProjectStatus


class TestTaskExecutionAgent:
    """Test the TaskExecutionAgent orchestrator"""
    
    @pytest.fixture
    def agent(self):
        """Create a TaskExecutionAgent for testing"""
        return TaskExecutionAgent()
    
    @pytest.fixture
    def project_state(self):
        """Create a ProjectState for testing"""
        return ProjectState(
            project_brief="Test project",
            status=ProjectStatus.QUILTING
        )
    
    @pytest.mark.asyncio
    async def test_plan_authentication_goal(self, agent):
        """Test Gemini Principle 1: Execution Rationale for authentication"""
        goal = "add authentication to the project"
        context = {"patterns": ["rest_api"], "framework": "express"}
        
        tasks, rationale = await agent.plan(goal, context)
        
        # Verify task list
        assert len(tasks) == 5
        assert "authentication library" in tasks[0].description
        assert "JWT strategy" in tasks[1].description
        assert "adapter code" in tasks[2].description
        assert "API endpoints" in tasks[3].description
        assert "integration tests" in tasks[4].description
        
        # Verify execution rationale (Gemini Principle 1)
        assert "Passport.js" in rationale
        assert "JWT strategy" in rationale
        assert "API endpoints" in rationale
        assert "secure, testable implementation" in rationale
        assert rationale.endswith(".")  # Proper sentence structure
    
    @pytest.mark.asyncio
    async def test_plan_component_search_goal(self, agent):
        """Test planning for component search goal"""
        goal = "search for components matching project requirements"
        context = {"patterns": ["rest_api", "message_queue"]}
        
        tasks, rationale = await agent.plan(goal, context)
        
        assert len(tasks) == 4
        assert "project requirements" in tasks[0].description
        assert "GitHub" in tasks[1].description
        assert "health analysis" in tasks[2].description
        assert "recommendations" in tasks[3].description
        
        # Verify rationale explains the approach
        assert "analyze your project requirements" in rationale
        assert "search GitHub" in rationale
        assert "health analysis" in rationale
        assert "high-quality components" in rationale
    
    @pytest.mark.asyncio
    async def test_plan_code_generation_goal(self, agent):
        """Test planning for code generation goal"""
        goal = "generate code for selected components"
        context = {"selected_components": {"rest_api": "express", "database": "mongoose"}}
        
        tasks, rationale = await agent.plan(goal, context)
        
        assert len(tasks) == 4
        assert "component interfaces" in tasks[0].description
        assert "adapter code" in tasks[1].description
        assert "project structure" in tasks[2].description
        assert "tests and documentation" in tasks[3].description
        
        assert "analyze selected components" in rationale
        assert "adapter code" in rationale
        assert "professional-grade codebase" in rationale
    
    @pytest.mark.asyncio
    async def test_plan_generic_goal(self, agent):
        """Test planning for generic/unknown goal"""
        goal = "optimize the database performance"
        context = {}
        
        tasks, rationale = await agent.plan(goal, context)
        
        assert len(tasks) == 3
        assert goal in tasks[0].description
        assert "primary task steps" in tasks[1].description
        assert "verify results" in tasks[2].description.lower()
        
        assert goal in rationale
        assert "manageable steps" in rationale
        assert "systematic approach" in rationale
    
    @pytest.mark.asyncio
    async def test_execute_successful_tasks(self, agent, project_state):
        """Test successful task execution with activity instrumentation"""
        tasks = [
            Task(description="Analyze requirements"),
            Task(description="Generate code"),
            Task(description="Run tests")
        ]
        
        # Mock activity callback to capture updates
        activity_updates = []
        def mock_callback(task_id, activity):
            activity_updates.append((task_id, activity))
        
        agent.set_activity_callback(mock_callback)
        
        result = await agent.execute(tasks, project_state)
        
        # Verify successful execution
        assert result.success is True
        assert len(result.completed_tasks) == 3
        assert result.failed_task is None
        assert result.total_duration_seconds > 0
        
        # Verify all tasks completed
        for task in result.completed_tasks:
            assert task.status == TaskStatus.COMPLETED
            assert task.start_time is not None
            assert task.end_time is not None
            assert task.current_activity is not None
        
        # Verify activity instrumentation (Gemini Principle 2)
        assert len(activity_updates) > 0
        # Should have updates for each task
        task_ids_with_updates = set(update[0] for update in activity_updates)
        assert len(task_ids_with_updates) == 3
        
        # Verify suggested actions (Gemini Principle 3)
        assert len(result.suggested_actions) == 3
        action_titles = [action.title for action in result.suggested_actions]
        assert "Download Project" in action_titles
        assert "Start New Goal" in action_titles
        assert "View Documentation" in action_titles
    
    @pytest.mark.asyncio
    async def test_execute_with_failure(self, agent, project_state):
        """Test execution with task failure (stop-on-failure strategy)"""
        tasks = [
            Task(description="Successful task"),
            Task(description="Failing task"),
            Task(description="Never executed task")
        ]
        
        # Mock the failing task execution
        original_execute = agent._execute_single_task
        async def mock_execute(task, state):
            if "Failing" in task.description:
                task.fail("Simulated failure")
                return False
            return await original_execute(task, state)
        
        agent._execute_single_task = mock_execute
        
        result = await agent.execute(tasks, project_state)
        
        # Verify stop-on-failure behavior
        assert result.success is False
        assert len(result.completed_tasks) == 1  # Only first task completed
        assert result.failed_task is not None
        assert result.failed_task.description == "Failing task"
        assert result.failed_task.status == TaskStatus.FAILED
        
        # Verify failure suggested actions
        assert len(result.suggested_actions) >= 2
        action_types = [action.action_type.value for action in result.suggested_actions]
        assert "edit_goal_replan" in action_types
        assert "report_issue" in action_types
    
    @pytest.mark.asyncio
    async def test_activity_callback_integration(self, agent, project_state):
        """Test Gemini Principle 2: Activity callback integration"""
        tasks = [Task(description="Test task")]
        
        # Capture activity updates
        activity_log = []
        def activity_callback(task_id, activity):
            activity_log.append(f"{task_id}: {activity}")
        
        agent.set_activity_callback(activity_callback)
        
        await agent.execute(tasks, project_state)
        
        # Verify activity updates were captured
        assert len(activity_log) > 0
        
        # Should have at least initialization and completion activities
        activities = [log.split(": ", 1)[1] for log in activity_log]
        assert any("Initializing" in activity for activity in activities)
        assert any("Completed" in activity or "successfully" in activity for activity in activities)
    
    @pytest.mark.asyncio
    async def test_project_state_integration(self, agent, project_state):
        """Test integration with ProjectState for task storage"""
        tasks = [Task(description="Test task")]
        
        # Verify initial state
        assert project_state.active_task_list is None
        
        await agent.execute(tasks, project_state)
        
        # Verify task list was cleared after execution
        assert project_state.active_task_list is None
        assert project_state.task_execution_context is None
    
    def test_activity_callback_error_handling(self, agent):
        """Test that activity callback errors don't break execution"""
        def failing_callback(task_id, activity):
            raise Exception("Callback error")
        
        agent.set_activity_callback(failing_callback)
        
        # Should not raise exception
        agent._notify_activity_update("test_id", "test_activity")
    
    def test_task_type_determination(self, agent):
        """Test task type determination logic"""
        # Component search task
        task1 = Task(description="Search for components")
        assert agent._determine_task_type(task1) == "component_search"
        
        # Code generation task
        task2 = Task(description="Generate adapter code")
        assert agent._determine_task_type(task2) == "code_generation"
        
        # Analysis task
        task3 = Task(description="Analyze requirements")
        assert agent._determine_task_type(task3) == "analysis"
        
        # Authentication task by metadata
        task4 = Task(description="Setup auth", metadata={"component_type": "authentication"})
        assert agent._determine_task_type(task4) == "authentication"
        
        # Generic task
        task5 = Task(description="Do something else")
        assert agent._determine_task_type(task5) == "generic"
    
    @pytest.mark.asyncio
    async def test_execution_result_summary_messages(self, agent, project_state):
        """Test that execution results include appropriate summary messages"""
        # Test successful execution
        tasks = [Task(description="Test task")]
        result = await agent.execute(tasks, project_state)
        
        assert result.success is True
        assert "Successfully completed all 1 tasks" in result.summary_message
        
        # Test failed execution
        tasks = [Task(description="Failing task")]
        
        # Mock failure
        original_execute = agent._execute_single_task
        async def mock_failing_execute(task, state):
            task.fail("Test failure")
            return False
        
        agent._execute_single_task = mock_failing_execute
        result = await agent.execute(tasks, project_state)
        
        assert result.success is False
        assert "Execution stopped at task: Failing task" in result.summary_message
