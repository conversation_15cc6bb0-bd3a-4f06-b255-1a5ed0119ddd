# TaskExecutionAgent API Reference

## Overview

The TaskExecutionAgent provides transparent, task-based execution for CodeQuilter operations. It implements Gemini's three core principles:

1. **Execution Rationale**: Every plan includes human-readable reasoning
2. **Agent Activity Instrumentation**: Real-time micro-progress updates
3. **Post-Execution User Actions**: Context-appropriate next steps

## Communication Channels

### WebSocket API (Real-time)
**Endpoint**: `ws://localhost:8000/ws/tasks/{session_id}`

For real-time task execution with live progress updates and interactive plan approval.

### REST API (Synchronous)
**Base Path**: `/api/projects/{session_id}/tasks`

For synchronous operations and simple integrations.

## WebSocket Message Types

### Client → Server Messages

#### PLAN_REQUEST
Request task plan generation.
```json
{
  "type": "PLAN_REQUEST",
  "goal": "add authentication to the project",
  "context": {
    "framework": "express",
    "patterns": ["rest_api"]
  }
}
```

#### PLAN_APPROVED
Approve and execute a generated plan.
```json
{
  "type": "PLAN_APPROVED",
  "approved": true,
  "tasks": [
    {
      "id": "task-uuid",
      "description": "Task description",
      "metadata": {}
    }
  ]
}
```

#### PING
Connection keepalive.
```json
{
  "type": "PING"
}
```

### Server → Client Messages

#### PLAN_GENERATED
Generated task plan with execution rationale.
```json
{
  "type": "PLAN_GENERATED",
  "goal": "add authentication to the project",
  "rationale": "To add authentication, I will first add the 'Passport.js' library, then generate a standard adapter to handle JWT strategy, and finally create the necessary API endpoints and integration tests. This plan ensures a secure, testable implementation with minimal custom code.",
  "tasks": [
    {
      "id": "auth-task-1",
      "description": "Search for authentication library (Passport.js)",
      "status": "pending",
      "metadata": {
        "component_type": "authentication",
        "library": "passport"
      }
    }
  ],
  "task_count": 5,
  "timestamp": "2025-01-20T10:00:00Z"
}
```

#### TASK_STATUS_UPDATE
Real-time task progress updates.
```json
{
  "type": "TASK_STATUS_UPDATE",
  "task_id": "auth-task-1",
  "activity": "Analyzing component interfaces...",
  "timestamp": "2025-01-20T10:01:30Z"
}
```

#### EXECUTION_COMPLETE
Final execution results with suggested actions.
```json
{
  "type": "EXECUTION_COMPLETE",
  "success": true,
  "summary_message": "Successfully completed all 5 tasks",
  "completed_tasks": [...],
  "failed_task": null,
  "total_duration_seconds": 45.2,
  "suggested_actions": [
    {
      "action_type": "download_project",
      "title": "Download Project",
      "description": "Download the generated codebase as a ZIP file",
      "enabled": true
    },
    {
      "action_type": "start_new_goal",
      "title": "Start New Goal",
      "description": "Give me a new high-level goal for this project",
      "enabled": true
    }
  ],
  "timestamp": "2025-01-20T10:05:45Z"
}
```

#### ERROR
Error message.
```json
{
  "type": "ERROR",
  "error": "Plan generation failed: Invalid goal format",
  "timestamp": "2025-01-20T10:00:15Z"
}
```

#### PONG
Keepalive response.
```json
{
  "type": "PONG",
  "timestamp": "2025-01-20T10:00:00Z"
}
```

## REST API Endpoints

### Generate Task Plan
**POST** `/api/projects/{session_id}/tasks/plan`

Generate a task plan for a given goal.

**Request Body:**
```json
{
  "goal": "add authentication to the project",
  "context": {
    "framework": "express",
    "patterns": ["rest_api"]
  }
}
```

**Response:**
```json
{
  "goal": "add authentication to the project",
  "rationale": "To add authentication, I will first add the 'Passport.js' library...",
  "tasks": [...],
  "task_count": 5
}
```

### Execute Task Plan
**POST** `/api/projects/{session_id}/tasks/execute`

Execute an approved task plan synchronously.

**Request Body:**
```json
{
  "tasks": [
    {
      "id": "task-uuid",
      "description": "Task description",
      "metadata": {}
    }
  ],
  "approved": true
}
```

**Response:**
```json
{
  "success": true,
  "summary_message": "Successfully completed all tasks",
  "completed_tasks": [...],
  "failed_task": null,
  "total_duration_seconds": 30.5,
  "suggested_actions": [...]
}
```

### Component Search Task
**POST** `/api/projects/{session_id}/tasks/component-search`

Execute component search using TaskExecutionAgent.

**Response:**
```json
{
  "success": true,
  "summary_message": "Component search completed successfully",
  "completed_tasks": [...],
  "suggested_actions": [...]
}
```

### Code Generation Task
**POST** `/api/projects/{session_id}/tasks/code-generation`

Execute code generation for selected components.

**Prerequisites:** Project must have selected components.

**Response:**
```json
{
  "success": true,
  "summary_message": "Code generation completed",
  "completed_tasks": [...],
  "suggested_actions": [...]
}
```

### Task Execution Status
**GET** `/api/projects/{session_id}/tasks/status`

Get current task execution status.

**Response:**
```json
{
  "session_id": "session-uuid",
  "has_active_tasks": true,
  "active_task_count": 3,
  "task_execution_context": {
    "execution_start": "2025-01-20T10:00:00Z"
  },
  "project_status": "quilting",
  "last_modified": "2025-01-20T10:05:00Z"
}
```

## Error Handling

### Error Types

#### Retryable Errors
- Network connection failures
- Temporary service unavailability
- Timeout errors

**Suggested Actions:**
- Retry Plan
- Edit Goal & Re-plan
- Report Issue

#### Permanent Errors
- Resource not found
- Invalid configuration
- Permission denied

**Suggested Actions:**
- Edit Goal & Re-plan
- Report Issue

### Error Response Format
```json
{
  "success": false,
  "summary_message": "Execution stopped at task: Component search",
  "failed_task": {
    "id": "search-task",
    "description": "Search for components",
    "status": "failed",
    "error_message": "Component not found in any repository"
  },
  "suggested_actions": [
    {
      "action_type": "edit_goal_replan",
      "title": "Edit Goal & Re-plan",
      "description": "Modify your original request and generate a new plan"
    }
  ]
}
```

## Integration Examples

### Frontend Integration (WebSocket)
```javascript
const ws = new WebSocket('ws://localhost:8000/ws/tasks/session-id');

// Send plan request
ws.send(JSON.stringify({
  type: 'PLAN_REQUEST',
  goal: 'add authentication',
  context: { framework: 'express' }
}));

// Handle responses
ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  
  switch (message.type) {
    case 'PLAN_GENERATED':
      // Show plan to user for approval
      showPlanApproval(message.tasks, message.rationale);
      break;
      
    case 'TASK_STATUS_UPDATE':
      // Update progress UI
      updateTaskProgress(message.task_id, message.activity);
      break;
      
    case 'EXECUTION_COMPLETE':
      // Show results and suggested actions
      showResults(message);
      break;
  }
};
```

### Backend Integration (REST)
```python
import requests

# Generate plan
response = requests.post(
    f'/api/projects/{session_id}/tasks/plan',
    json={
        'goal': 'add authentication',
        'context': {'framework': 'express'}
    }
)
plan = response.json()

# Execute plan
response = requests.post(
    f'/api/projects/{session_id}/tasks/execute',
    json={
        'tasks': plan['tasks'],
        'approved': True
    }
)
result = response.json()
```

## Task Types

### Authentication Tasks
- Library selection and integration
- Security middleware setup
- API endpoint generation
- Test creation

### Component Search Tasks
- Requirement analysis
- GitHub repository search
- Health analysis
- Recommendation generation

### Code Generation Tasks
- Interface analysis
- Adapter code generation
- Project structure creation
- Documentation generation

### Analysis Tasks
- Requirement extraction
- Pattern analysis
- Compatibility assessment
- Risk evaluation

## Best Practices

1. **Always handle WebSocket disconnections** and implement reconnection logic
2. **Use appropriate communication channel**: WebSocket for real-time, REST for simple operations
3. **Implement proper error handling** for both retryable and permanent failures
4. **Show execution rationale** to users for transparency
5. **Display real-time progress** for better user experience
6. **Provide suggested actions** after task completion
7. **Validate task approval** before execution
8. **Monitor task execution status** for long-running operations
