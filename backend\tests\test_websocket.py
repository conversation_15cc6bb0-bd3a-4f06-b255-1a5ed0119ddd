"""
Tests for TaskExecutionAgent WebSocket API.

Tests real-time communication including:
- Plan generation and approval workflow
- Real-time task status updates
- Execution completion with suggested actions
- Connection management and error handling
"""

import pytest
import json
import asyncio
from unittest.mock import Mock, patch, AsyncMock

from fastapi.testclient import TestClient
from fastapi.websockets import WebSocketDisconnect

from backend.src.main import app
from backend.src.api.websocket import TaskExecutionWebSocketManager, WebSocketMessageType
from backend.src.agents.task_models import Task, TaskStatus
from backend.src.state.project_state import ProjectState


class TestTaskExecutionWebSocketManager:
    """Test the WebSocket manager for TaskExecutionAgent"""
    
    @pytest.fixture
    def manager(self):
        """Create a WebSocket manager for testing"""
        return TaskExecutionWebSocketManager()
    
    @pytest.fixture
    def mock_websocket(self):
        """Create a mock WebSocket for testing"""
        from fastapi.websockets import WebSocketState
        websocket = Mock()
        websocket.client_state = WebSocketState.CONNECTED
        websocket.accept = AsyncMock()
        websocket.send_text = AsyncMock()
        websocket.receive_text = AsyncMock()
        return websocket
    
    @pytest.mark.asyncio
    async def test_websocket_connection(self, manager, mock_websocket):
        """Test WebSocket connection establishment"""
        session_id = "test_session"
        
        await manager.connect(mock_websocket, session_id)
        
        # Verify connection setup
        assert session_id in manager.active_connections
        assert session_id in manager.task_agents
        assert session_id in manager.message_queue
        
        # Verify WebSocket was accepted
        mock_websocket.accept.assert_called_once()

        # Wait a moment for async message sending
        await asyncio.sleep(0.1)

        # Verify connection message was sent
        mock_websocket.send_text.assert_called_once()
        sent_message = json.loads(mock_websocket.send_text.call_args[0][0])
        assert sent_message["type"] == "CONNECTION_ESTABLISHED"
        assert sent_message["session_id"] == session_id
    
    def test_websocket_disconnection(self, manager, mock_websocket):
        """Test WebSocket disconnection cleanup"""
        session_id = "test_session"
        
        # Setup connection
        manager.active_connections[session_id] = mock_websocket
        manager.task_agents[session_id] = Mock()
        manager.message_queue[session_id] = []
        
        # Disconnect
        manager.disconnect(session_id)
        
        # Verify cleanup
        assert session_id not in manager.active_connections
        assert session_id not in manager.task_agents
        assert session_id not in manager.message_queue
    
    @pytest.mark.asyncio
    async def test_plan_request_handling(self, manager, mock_websocket):
        """Test plan generation request handling"""
        session_id = "test_session"
        await manager.connect(mock_websocket, session_id)
        
        # Mock the plan method
        mock_agent = manager.task_agents[session_id]
        mock_tasks = [
            Task(description="Test task 1"),
            Task(description="Test task 2")
        ]
        mock_rationale = "Test rationale for the plan"
        mock_agent.plan = AsyncMock(return_value=(mock_tasks, mock_rationale))
        
        # Handle plan request
        goal = "test goal"
        context = {"test": "context"}
        await manager.handle_plan_request(session_id, goal, context)

        # Wait for async operations
        await asyncio.sleep(0.1)

        # Verify plan was called
        mock_agent.plan.assert_called_once_with(goal, context)

        # Verify plan message was sent
        assert mock_websocket.send_text.call_count >= 2  # Connection + plan message
        
        # Find the plan message
        plan_message = None
        for call in mock_websocket.send_text.call_args_list:
            message = json.loads(call[0][0])
            if message.get("type") == WebSocketMessageType.PLAN_GENERATED:
                plan_message = message
                break
        
        assert plan_message is not None
        assert plan_message["goal"] == goal
        assert plan_message["rationale"] == mock_rationale  # Gemini Principle 1
        assert len(plan_message["tasks"]) == 2
        assert plan_message["task_count"] == 2
    
    @pytest.mark.asyncio
    async def test_plan_approval_success(self, manager, mock_websocket):
        """Test successful plan approval and execution"""
        session_id = "test_session"
        await manager.connect(mock_websocket, session_id)
        
        # Mock project state
        project_state = ProjectState(session_id=session_id)
        with patch('backend.src.api.websocket.session_manager') as mock_session_manager:
            mock_session_manager.get_project.return_value = project_state
            
            # Mock agent execution
            mock_agent = manager.task_agents[session_id]
            mock_result = Mock()
            mock_result.success = True
            mock_result.summary_message = "All tasks completed successfully"
            mock_result.completed_tasks = [Task(description="Completed task")]
            mock_result.failed_task = None
            mock_result.total_duration_seconds = 30.5
            mock_result.to_dict.return_value = {
                "suggested_actions": [
                    {"action_type": "download_project", "title": "Download Project"}
                ]
            }
            mock_agent.execute = AsyncMock(return_value=mock_result)
            
            # Handle plan approval
            tasks_data = [
                {"id": "task1", "description": "Test task", "metadata": {}}
            ]
            await manager.handle_plan_approval(session_id, True, tasks_data)

            # Wait for async operations
            await asyncio.sleep(0.1)

            # Verify execution was called
            mock_agent.execute.assert_called_once()
            
            # Verify completion message was sent
            completion_message = None
            for call in mock_websocket.send_text.call_args_list:
                message = json.loads(call[0][0])
                if message.get("type") == WebSocketMessageType.EXECUTION_COMPLETE:
                    completion_message = message
                    break
            
            assert completion_message is not None
            assert completion_message["success"] is True
            assert completion_message["summary_message"] == "All tasks completed successfully"
            assert len(completion_message["suggested_actions"]) == 1  # Gemini Principle 3
    
    @pytest.mark.asyncio
    async def test_plan_rejection(self, manager, mock_websocket):
        """Test plan rejection handling"""
        session_id = "test_session"
        await manager.connect(mock_websocket, session_id)
        
        # Handle plan rejection
        await manager.handle_plan_approval(session_id, False, [])

        # Wait for async operations
        await asyncio.sleep(0.1)

        # Verify rejection message was sent
        rejection_message = None
        for call in mock_websocket.send_text.call_args_list:
            message = json.loads(call[0][0])
            if message.get("type") == "PLAN_REJECTED":
                rejection_message = message
                break
        
        assert rejection_message is not None
        assert rejection_message["message"] == "Plan rejected by user"
    
    @pytest.mark.asyncio
    async def test_activity_callback(self, manager, mock_websocket):
        """Test Gemini Principle 2: Activity callback for real-time updates"""
        session_id = "test_session"
        await manager.connect(mock_websocket, session_id)
        
        # Get the activity callback
        agent = manager.task_agents[session_id]
        callback = manager._create_activity_callback(session_id)
        
        # Simulate activity update
        task_id = "test_task"
        activity = "Processing data..."
        callback(task_id, activity)  # This is now synchronous

        # Allow message batching to process
        await asyncio.sleep(0.4)  # Wait longer than batch interval
        
        # Verify activity update message was sent
        activity_message = None
        for call in mock_websocket.send_text.call_args_list:
            message = json.loads(call[0][0])
            if message.get("type") == WebSocketMessageType.TASK_STATUS_UPDATE:
                activity_message = message
                break
        
        assert activity_message is not None
        assert activity_message["task_id"] == task_id
        assert activity_message["activity"] == activity
    
    @pytest.mark.asyncio
    async def test_error_handling(self, manager, mock_websocket):
        """Test error handling and reporting"""
        session_id = "test_session"
        await manager.connect(mock_websocket, session_id)
        
        # Simulate error during plan generation
        mock_agent = manager.task_agents[session_id]
        mock_agent.plan = AsyncMock(side_effect=Exception("Test error"))
        
        await manager.handle_plan_request(session_id, "test goal", {})

        # Wait for async operations
        await asyncio.sleep(0.1)

        # Verify error message was sent
        error_message = None
        for call in mock_websocket.send_text.call_args_list:
            message = json.loads(call[0][0])
            if message.get("type") == WebSocketMessageType.ERROR:
                error_message = message
                break
        
        assert error_message is not None
        assert "Test error" in error_message["error"]
    
    @pytest.mark.asyncio
    async def test_ping_pong(self, manager, mock_websocket):
        """Test ping/pong for connection keepalive"""
        session_id = "test_session"
        await manager.connect(mock_websocket, session_id)
        
        await manager.handle_ping(session_id)

        # Wait for async operations
        await asyncio.sleep(0.1)

        # Verify pong message was sent
        pong_message = None
        for call in mock_websocket.send_text.call_args_list:
            message = json.loads(call[0][0])
            if message.get("type") == WebSocketMessageType.PONG:
                pong_message = message
                break
        
        assert pong_message is not None
    
    @pytest.mark.asyncio
    async def test_message_batching(self, manager, mock_websocket):
        """Test message batching for performance"""
        session_id = "test_session"
        await manager.connect(mock_websocket, session_id)
        
        # Queue multiple messages quickly
        for i in range(3):
            await manager._queue_message(session_id, {
                "type": "TEST_MESSAGE",
                "index": i
            })
        
        # Messages should be queued, not sent immediately
        initial_call_count = mock_websocket.send_text.call_count
        
        # Wait for batch processing
        await asyncio.sleep(0.4)
        
        # Verify messages were sent after batching
        final_call_count = mock_websocket.send_text.call_count
        assert final_call_count > initial_call_count


class TestWebSocketIntegration:
    """Integration tests for WebSocket endpoint"""
    
    def test_websocket_endpoint_exists(self):
        """Test that WebSocket endpoint is properly configured"""
        client = TestClient(app)
        
        # Test that the WebSocket route exists
        # Note: TestClient doesn't support WebSocket testing directly,
        # but we can verify the route is configured
        assert any(
            route.path == "/ws/tasks/{session_id}" 
            for route in app.routes
        )
    
    @pytest.mark.asyncio
    async def test_websocket_message_types(self):
        """Test WebSocket message type constants"""
        assert WebSocketMessageType.PLAN_GENERATED == "PLAN_GENERATED"
        assert WebSocketMessageType.PLAN_APPROVED == "PLAN_APPROVED"
        assert WebSocketMessageType.PLAN_REJECTED == "PLAN_REJECTED"
        assert WebSocketMessageType.TASK_STATUS_UPDATE == "TASK_STATUS_UPDATE"
        assert WebSocketMessageType.EXECUTION_COMPLETE == "EXECUTION_COMPLETE"
        assert WebSocketMessageType.ERROR == "ERROR"
        assert WebSocketMessageType.PING == "PING"
        assert WebSocketMessageType.PONG == "PONG"
